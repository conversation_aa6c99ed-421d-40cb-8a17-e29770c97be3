#!/usr/bin/env python3
"""
Comprehensive test for PostgreSQL integration with orchestration memory system.
"""
import asyncio
import sys
import os
from datetime import datetime, timezone

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from backend.orchestration.memory import MemoryUnits, GlobalMemory
from backend.orchestration.state import OrchestrationStep, LangGraphState
from backend.orchestration.db import save_state
from backend.app.core.db.database import check_database_integrity

async def test_comprehensive_memory_integration():
    """Test comprehensive PostgreSQL integration for memory system."""
    print("🧪 Testing Comprehensive Memory PostgreSQL Integration")
    print("=" * 60)
    
    # 1. Database Health Check
    print("\n1. Database Health Check...")
    health = check_database_integrity()
    print(f"   Database Status: {health['overall_status']}")
    if health['errors']:
        print(f"   Errors: {health['errors']}")
        return False
    
    # 2. Create test state
    print("\n2. Creating test state...")
    try:
        test_state = LangGraphState(
            id="comprehensive-test-state",
            user_input="Comprehensive test for memory PostgreSQL integration",
            context={},
            coordinator_plan_id=None,
            tasks=[],
            current_task_id=None,
            current_step=OrchestrationStep.PLANNING
        )
        
        state_id = await save_state(test_state)
        print(f"   ✅ Created state: {state_id}")
    except Exception as e:
        print(f"   ❌ State creation failed: {e}")
        return False
    
    # 3. Initialize GlobalMemory
    print("\n3. Initializing GlobalMemory...")
    try:
        global_memory = GlobalMemory()
        print("   ✅ GlobalMemory initialized")
    except Exception as e:
        print(f"   ❌ GlobalMemory initialization failed: {e}")
        return False
    
    # 4. Test memory addition with PostgreSQL persistence
    print("\n4. Testing memory addition...")
    try:
        memory_ids = []
        
        # Add multiple memories
        for i in range(5):
            memory_id = await global_memory.add_memory(
                content=f"Test memory {i+1} for comprehensive PostgreSQL integration testing.",
                agent=f"test-agent-{i+1}",
                current_step=OrchestrationStep.PLANNING if i % 2 == 0 else OrchestrationStep.TASK_EXECUTION,
                task_id=f"test-task-{i+1}",
                metadata={"test_index": i+1, "batch": "comprehensive"},
                tags=["test", "comprehensive", f"batch-{i//2}"],
                state_id=state_id
            )
            memory_ids.append(memory_id)
        
        print(f"   ✅ Added {len(memory_ids)} memories to PostgreSQL")
    except Exception as e:
        print(f"   ❌ Memory addition failed: {e}")
        return False
    
    # 5. Test memory retrieval from PostgreSQL
    print("\n5. Testing memory retrieval...")
    try:
        retrieved_memories = await global_memory.retrieve_memory(
            state_id=state_id,
            limit=10
        )
        print(f"   ✅ Retrieved {len(retrieved_memories)} memories from PostgreSQL")
        
        if len(retrieved_memories) != 5:
            print(f"   ⚠️ Expected 5 memories, got {len(retrieved_memories)}")
    except Exception as e:
        print(f"   ❌ Memory retrieval failed: {e}")
        return False
    
    # 6. Test memory search functionality
    print("\n6. Testing memory search...")
    try:
        search_results = await global_memory.search_memory(
            query="comprehensive PostgreSQL integration",
            state_id=state_id,
            limit=3,
            search_type="hybrid"
        )
        print(f"   ✅ Search returned {len(search_results)} results")
    except Exception as e:
        print(f"   ❌ Memory search failed: {e}")
        return False
    
    # 7. Test memory filtering
    print("\n7. Testing memory filtering...")
    try:
        # Filter by agent
        agent_memories = await global_memory.retrieve_memory(
            state_id=state_id,
            agent="test-agent-1",
            limit=10
        )
        print(f"   ✅ Agent filter returned {len(agent_memories)} memories")
        
        # Filter by tags
        tag_memories = await global_memory.retrieve_memory(
            state_id=state_id,
            tags=["batch-0"],
            limit=10
        )
        print(f"   ✅ Tag filter returned {len(tag_memories)} memories")
    except Exception as e:
        print(f"   ❌ Memory filtering failed: {e}")
        return False
    
    # 8. Test memory update
    print("\n8. Testing memory update...")
    try:
        if memory_ids:
            success = await global_memory.update_memory(
                memory_id=memory_ids[0],
                content="Updated content for comprehensive testing",
                metadata={"updated": True, "test_index": 1},
                tags=["test", "comprehensive", "updated"],
                state_id=state_id
            )
            print(f"   ✅ Memory update: {'successful' if success else 'failed'}")
    except Exception as e:
        print(f"   ❌ Memory update failed: {e}")
        return False
    
    # 9. Test memory statistics
    print("\n9. Testing memory statistics...")
    try:
        stats = await global_memory.get_memory_statistics(state_id=state_id)
        print(f"   ✅ Statistics: {stats['total_memories']} total memories")
        print(f"   ✅ Agents: {list(stats['memories_by_agent'].keys())}")
        print(f"   ✅ Steps: {list(stats['memories_by_step'].keys())}")
    except Exception as e:
        print(f"   ❌ Memory statistics failed: {e}")
        return False
    
    # 10. Test health check
    print("\n10. Testing health check...")
    try:
        health = await global_memory.health_check()
        print(f"   ✅ Health status: {health['status']}")
        if health['errors']:
            print(f"   ⚠️ Errors: {health['errors']}")
        if health['warnings']:
            print(f"   ⚠️ Warnings: {health['warnings']}")
    except Exception as e:
        print(f"   ❌ Health check failed: {e}")
        return False
    
    # 11. Test context injection
    print("\n11. Testing context injection...")
    try:
        updated_state = await global_memory.inject_into_context(test_state)
        memory_context = updated_state.context.get("memory_context", [])
        print(f"   ✅ Injected {len(memory_context)} memories into context")
    except Exception as e:
        print(f"   ❌ Context injection failed: {e}")
        return False
    
    # 12. Test memory deletion
    print("\n12. Testing memory deletion...")
    try:
        if memory_ids:
            success = await global_memory.delete_memory(
                memory_id=memory_ids[-1],
                state_id=state_id
            )
            print(f"   ✅ Memory deletion: {'successful' if success else 'failed'}")
    except Exception as e:
        print(f"   ❌ Memory deletion failed: {e}")
        return False
    
    # 13. Cleanup
    print("\n13. Cleaning up...")
    try:
        from backend.app.core.db.database import get_db_context
        from sqlalchemy import text
        
        with get_db_context() as db:
            db.execute(text("DELETE FROM orchestration_memory WHERE state_id = 'comprehensive-test-state'"))
            db.execute(text("DELETE FROM orchestration_states WHERE id = 'comprehensive-test-state'"))
            db.commit()
        print("   ✅ Cleanup completed")
    except Exception as e:
        print(f"   ⚠️ Cleanup failed: {e}")
    
    print("\n🎉 All comprehensive PostgreSQL integration tests passed!")
    return True

if __name__ == "__main__":
    async def main():
        try:
            success = await test_comprehensive_memory_integration()
            sys.exit(0 if success else 1)
        except Exception as e:
            print(f"\n❌ Test failed with exception: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    
    asyncio.run(main())
