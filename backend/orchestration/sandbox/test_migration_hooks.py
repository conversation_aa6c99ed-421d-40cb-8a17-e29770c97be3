#!/usr/bin/env python3
"""
Test migration hooks functionality.
"""
import asyncio
import sys
import os
import json
import tempfile
from pathlib import Path

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from backend.scripts.migration_hooks import (
    DataTransformationHooks,
    migration_hooks,
    transform_legacy_memory_format,
    backfill_data_from_source
)

async def test_transformation_hooks():
    """Test data transformation hooks."""
    print("\n🔄 Testing Data Transformation Hooks")
    print("-" * 40)
    
    # Create test hooks instance
    hooks = DataTransformationHooks()
    
    # Test pre-hook registration and execution
    def test_pre_hook(context):
        context["pre_hook_executed"] = True
        return context
    
    hooks.register_pre_hook(test_pre_hook)
    
    # Test post-hook registration and execution
    async def test_post_hook(context):
        context["post_hook_executed"] = True
        return context
    
    hooks.register_post_hook(test_post_hook)
    
    # Execute hooks
    context = {"test": True}
    context = await hooks.execute_pre_hooks(context)
    context = await hooks.execute_post_hooks(context)
    
    # Verify results
    if context.get("pre_hook_executed") and context.get("post_hook_executed"):
        print("   ✅ Hook registration and execution: PASSED")
        return True
    else:
        print("   ❌ Hook registration and execution: FAILED")
        return False

def test_data_transformation():
    """Test data transformation rules."""
    print("\n🔄 Testing Data Transformation Rules")
    print("-" * 40)
    
    # Test legacy memory format transformation
    legacy_data = {
        "old_content_field": "This is legacy content",
        "old_agent_field": "legacy-agent",
        "some_other_field": "preserved"
    }
    
    transformed = transform_legacy_memory_format(legacy_data)
    
    # Check transformation results
    checks = [
        ("content" in transformed, "Content field transformation"),
        ("agent" in transformed, "Agent field transformation"),
        ("old_content_field" not in transformed, "Old content field removal"),
        ("old_agent_field" not in transformed, "Old agent field removal"),
        ("current_step" in transformed, "Default current_step addition"),
        ("metadata" in transformed, "Default metadata addition"),
        ("tags" in transformed, "Default tags addition"),
        (transformed["some_other_field"] == "preserved", "Field preservation")
    ]
    
    all_passed = True
    for check, description in checks:
        if check:
            print(f"   ✅ {description}: PASSED")
        else:
            print(f"   ❌ {description}: FAILED")
            all_passed = False
    
    return all_passed

async def test_backfill_from_json():
    """Test data backfill from JSON file."""
    print("\n🔄 Testing Data Backfill from JSON")
    print("-" * 40)
    
    # Create temporary JSON file with test data
    test_data = {
        "memories": [
            {
                "old_content_field": "Test memory 1",
                "old_agent_field": "test-agent-1",
                "state_id": "test-state-1",
                "metadata": {"test": True}
            },
            {
                "old_content_field": "Test memory 2", 
                "old_agent_field": "test-agent-2",
                "state_id": "test-state-2",
                "tags": ["test", "migration"]
            }
        ]
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(test_data, f)
        temp_file = f.name
    
    try:
        # Configure backfill
        config = {
            "type": "json_file",
            "file_path": temp_file,
            "transform_rule": "legacy_memory"
        }
        
        # Run backfill
        results = await backfill_data_from_source(config)
        
        # Check results
        if (results["status"] == "completed" and 
            results["records_processed"] == 2 and 
            results["records_migrated"] == 2):
            print("   ✅ JSON backfill: PASSED")
            return True
        else:
            print(f"   ❌ JSON backfill: FAILED - {results}")
            return False
            
    except Exception as e:
        print(f"   ❌ JSON backfill: FAILED - {e}")
        return False
    finally:
        # Clean up temp file
        Path(temp_file).unlink(missing_ok=True)

async def test_hook_error_handling():
    """Test error handling in hooks."""
    print("\n🔄 Testing Hook Error Handling")
    print("-" * 40)
    
    hooks = DataTransformationHooks()
    
    # Register a hook that will fail
    def failing_hook(context):
        raise Exception("Test hook failure")
    
    hooks.register_pre_hook(failing_hook)
    
    # Test that error is properly handled
    try:
        await hooks.execute_pre_hooks({"test": True})
        print("   ❌ Error handling: FAILED (should have raised exception)")
        return False
    except Exception as e:
        if "Test hook failure" in str(e):
            print("   ✅ Error handling: PASSED")
            return True
        else:
            print(f"   ❌ Error handling: FAILED (wrong error: {e})")
            return False

def test_transformation_rule_registration():
    """Test transformation rule registration and usage."""
    print("\n🔄 Testing Transformation Rule Registration")
    print("-" * 40)
    
    hooks = DataTransformationHooks()
    
    # Register a custom transformation rule
    def custom_transform(data):
        data["transformed"] = True
        return data
    
    hooks.register_transformation_rule("custom", custom_transform)
    
    # Test transformation
    test_data = {"original": "data"}
    transformed = hooks.transform_data(test_data, "custom")
    
    if transformed.get("transformed") and transformed.get("original") == "data":
        print("   ✅ Transformation rule registration: PASSED")
        return True
    else:
        print("   ❌ Transformation rule registration: FAILED")
        return False

async def test_global_hooks_integration():
    """Test integration with global migration hooks."""
    print("\n🔄 Testing Global Hooks Integration")
    print("-" * 40)
    
    # Test that global hooks are properly configured
    if (len(migration_hooks.pre_hooks) >= 2 and 
        len(migration_hooks.post_hooks) >= 2 and
        len(migration_hooks.transformation_rules) >= 2):
        print("   ✅ Global hooks configuration: PASSED")
    else:
        print("   ❌ Global hooks configuration: FAILED")
        return False
    
    # Test transformation rules are accessible
    test_data = {"old_content_field": "test"}
    try:
        transformed = migration_hooks.transform_data(test_data, "legacy_memory")
        if "content" in transformed:
            print("   ✅ Global transformation rules: PASSED")
            return True
        else:
            print("   ❌ Global transformation rules: FAILED")
            return False
    except Exception as e:
        print(f"   ❌ Global transformation rules: FAILED - {e}")
        return False

async def test_migration_context_flow():
    """Test migration context flow through hooks."""
    print("\n🔄 Testing Migration Context Flow")
    print("-" * 40)
    
    hooks = DataTransformationHooks()
    
    # Register hooks that modify context
    def pre_hook_1(context):
        context["step_1"] = "completed"
        return context
    
    def pre_hook_2(context):
        context["step_2"] = "completed"
        context["total_steps"] = 2
        return context
    
    async def post_hook_1(context):
        context["post_step_1"] = "completed"
        return context
    
    hooks.register_pre_hook(pre_hook_1)
    hooks.register_pre_hook(pre_hook_2)
    hooks.register_post_hook(post_hook_1)
    
    # Execute hooks and track context
    context = {"initial": True}
    context = await hooks.execute_pre_hooks(context)
    context = await hooks.execute_post_hooks(context)
    
    # Verify context flow
    expected_keys = ["initial", "step_1", "step_2", "total_steps", "post_step_1"]
    if all(key in context for key in expected_keys):
        print("   ✅ Migration context flow: PASSED")
        return True
    else:
        print(f"   ❌ Migration context flow: FAILED - Missing keys in context: {context}")
        return False

async def main():
    """Run all migration hook tests."""
    print("🔄 Migration Hooks Tests")
    print("=" * 40)
    
    test_results = []
    
    tests = [
        ("Transformation Hooks", test_transformation_hooks),
        ("Data Transformation", test_data_transformation),
        ("Backfill from JSON", test_backfill_from_json),
        ("Hook Error Handling", test_hook_error_handling),
        ("Transformation Rule Registration", test_transformation_rule_registration),
        ("Global Hooks Integration", test_global_hooks_integration),
        ("Migration Context Flow", test_migration_context_flow),
    ]
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                success = await test_func()
            else:
                success = test_func()
            test_results.append((test_name, success))
        except Exception as e:
            print(f"   ❌ {test_name} failed with exception: {e}")
            test_results.append((test_name, False))
    
    # Summary
    print("\n📊 Migration Hook Test Results")
    print("=" * 40)
    passed = 0
    for test_name, success in test_results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        print("🎉 All migration hook tests PASSED!")
        return True
    else:
        print("⚠️ Some migration hook tests FAILED!")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
