#!/usr/bin/env python3
"""
Security tests for the memory persistence system.
"""
import asyncio
import sys
import os
import uuid

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from backend.orchestration.memory import (
    GlobalMemory, 
    ContentSanitizer, 
    SecurityViolationError,
    MemoryValidationError
)
from backend.orchestration.state import OrchestrationStep, LangGraphState
from backend.orchestration.db import save_state

async def test_content_sanitization():
    """Test content sanitization features."""
    print("\n🔒 Testing Content Sanitization")
    print("-" * 40)
    
    # Test basic HTML escaping
    dangerous_content = '<script>alert("xss")</script>Hello World'
    sanitized = ContentSanitizer.sanitize_content(dangerous_content)
    
    if '&lt;script&gt;' in sanitized and '<script>' not in sanitized:
        print("   ✅ HTML escaping: PASSED")
    else:
        print("   ❌ HTML escaping: FAILED")
        return False
    
    # Test sensitive data redaction in strict mode
    sensitive_content = "My <NAME_EMAIL> and my phone is ************"
    sanitized_strict = ContentSanitizer.sanitize_content(sensitive_content, strict_mode=True)
    
    if '[REDACTED_EMAIL]' in sanitized_strict and '[REDACTED_PHONE]' in sanitized_strict:
        print("   ✅ Sensitive data redaction: PASSED")
    else:
        print("   ❌ Sensitive data redaction: FAILED")
        return False
    
    # Test injection pattern removal
    injection_content = "SELECT * FROM users; DROP TABLE users;"
    sanitized_injection = ContentSanitizer.sanitize_content(injection_content)
    
    if '[REMOVED_UNSAFE_CONTENT]' in sanitized_injection:
        print("   ✅ Injection pattern removal: PASSED")
    else:
        print("   ❌ Injection pattern removal: FAILED")
        return False
    
    return True

async def test_security_validation():
    """Test security validation features."""
    print("\n🔒 Testing Security Validation")
    print("-" * 40)
    
    try:
        # Test script injection detection
        ContentSanitizer.validate_security(
            '<script>alert("hack")</script>',
            'test-agent'
        )
        print("   ❌ Script injection detection: FAILED (should have raised exception)")
        return False
    except SecurityViolationError:
        print("   ✅ Script injection detection: PASSED")
    
    try:
        # Test invalid agent identifier
        ContentSanitizer.validate_security(
            'Safe content',
            'invalid@agent!'
        )
        print("   ❌ Invalid agent detection: FAILED (should have raised exception)")
        return False
    except SecurityViolationError:
        print("   ✅ Invalid agent detection: PASSED")
    
    try:
        # Test dangerous metadata
        ContentSanitizer.validate_security(
            'Safe content',
            'safe-agent',
            {'dangerous': '<script>alert("xss")</script>'}
        )
        print("   ❌ Dangerous metadata detection: FAILED (should have raised exception)")
        return False
    except SecurityViolationError:
        print("   ✅ Dangerous metadata detection: PASSED")
    
    # Test valid content passes
    try:
        ContentSanitizer.validate_security(
            'This is safe content',
            'safe-agent',
            {'safe': 'metadata'}
        )
        print("   ✅ Valid content validation: PASSED")
    except SecurityViolationError:
        print("   ❌ Valid content validation: FAILED (should not raise exception)")
        return False
    
    return True

async def test_access_control():
    """Test agent access control features."""
    print("\n🔒 Testing Access Control")
    print("-" * 40)
    
    state_id = f"security-test-{uuid.uuid4().hex[:8]}"
    test_state = LangGraphState(
        id=state_id,
        user_input="Test access control",
        context={},
        coordinator_plan_id=None,
        tasks=[],
        current_task_id=None,
        current_step=OrchestrationStep.PLANNING
    )
    
    await save_state(test_state)
    
    # Test with restricted agents
    memory = GlobalMemory(auto_persist=True, session_state_id=state_id)
    memory.configure_security(
        enabled=True,
        strict_sanitization=True,
        allowed_agents=['allowed-agent', 'another-allowed-agent']
    )
    
    # Test allowed agent
    try:
        await memory.add_memory(
            content="Test content from allowed agent",
            agent="allowed-agent",
            current_step=OrchestrationStep.PLANNING
        )
        print("   ✅ Allowed agent access: PASSED")
    except SecurityViolationError:
        print("   ❌ Allowed agent access: FAILED (should be allowed)")
        return False
    
    # Test denied agent
    try:
        await memory.add_memory(
            content="Test content from denied agent",
            agent="denied-agent",
            current_step=OrchestrationStep.PLANNING
        )
        print("   ❌ Denied agent access: FAILED (should be denied)")
        return False
    except SecurityViolationError:
        print("   ✅ Denied agent access: PASSED")
    
    # Test retrieval with denied agent
    try:
        await memory.retrieve_memory(agent="denied-agent", state_id=state_id)
        print("   ❌ Denied agent retrieval: FAILED (should be denied)")
        return False
    except SecurityViolationError:
        print("   ✅ Denied agent retrieval: PASSED")
    
    return True

async def test_security_audit():
    """Test security audit functionality."""
    print("\n🔒 Testing Security Audit")
    print("-" * 40)
    
    state_id = f"audit-test-{uuid.uuid4().hex[:8]}"
    test_state = LangGraphState(
        id=state_id,
        user_input="Test security audit",
        context={},
        coordinator_plan_id=None,
        tasks=[],
        current_task_id=None,
        current_step=OrchestrationStep.PLANNING
    )
    
    await save_state(test_state)
    
    memory = GlobalMemory(auto_persist=True, session_state_id=state_id)
    memory.configure_security(enabled=False)  # Disable security for testing
    
    # Add some test memories with potential issues
    await memory.add_memory(
        content="Safe content",
        agent="test-agent",
        current_step=OrchestrationStep.PLANNING
    )
    
    await memory.add_memory(
        content="Contact <NAME_EMAIL> for more info",
        agent="test-agent",
        current_step=OrchestrationStep.PLANNING
    )
    
    # Run security audit
    audit_results = await memory.security_audit(state_id)
    
    # Check audit results
    if audit_results["status"] in ["warning", "violations_detected"]:
        print("   ✅ Security audit detection: PASSED")
    else:
        print("   ❌ Security audit detection: FAILED")
        return False
    
    if not audit_results["checks"]["security_enabled"]:
        print("   ✅ Security configuration check: PASSED")
    else:
        print("   ❌ Security configuration check: FAILED")
        return False
    
    if len(audit_results["recommendations"]) > 0:
        print("   ✅ Security recommendations: PASSED")
    else:
        print("   ❌ Security recommendations: FAILED")
        return False
    
    return True

async def test_strict_sanitization():
    """Test strict sanitization mode."""
    print("\n🔒 Testing Strict Sanitization")
    print("-" * 40)
    
    state_id = f"sanitization-test-{uuid.uuid4().hex[:8]}"
    test_state = LangGraphState(
        id=state_id,
        user_input="Test strict sanitization",
        context={},
        coordinator_plan_id=None,
        tasks=[],
        current_task_id=None,
        current_step=OrchestrationStep.PLANNING
    )
    
    await save_state(test_state)
    
    memory = GlobalMemory(auto_persist=True, session_state_id=state_id)
    memory.configure_security(
        enabled=True,
        strict_sanitization=True,
        allowed_agents=['test-agent']
    )
    
    # Add memory with sensitive content
    original_content = "My <NAME_EMAIL> and my SSN is ***********"
    memory_id = await memory.add_memory(
        content=original_content,
        agent="test-agent",
        current_step=OrchestrationStep.PLANNING
    )
    
    # Retrieve and check if content was sanitized
    memories = await memory.retrieve_memory(state_id=state_id, limit=1)
    
    if len(memories) > 0:
        stored_content = memories[0].content
        if '[REDACTED_EMAIL]' in stored_content and '[REDACTED_SSN]' in stored_content:
            print("   ✅ Strict sanitization: PASSED")
            return True
        else:
            print(f"   ❌ Strict sanitization: FAILED (content: {stored_content})")
            return False
    else:
        print("   ❌ Strict sanitization: FAILED (no memories retrieved)")
        return False

async def cleanup_test_data(state_ids):
    """Clean up test data."""
    print("\n🧹 Cleaning up test data...")
    try:
        from backend.app.core.db.database import get_db_context
        from sqlalchemy import text
        
        with get_db_context() as db:
            for state_id in state_ids:
                db.execute(text("DELETE FROM orchestration_memory WHERE state_id = :state_id"), {"state_id": state_id})
                db.execute(text("DELETE FROM orchestration_states WHERE id = :state_id"), {"state_id": state_id})
            db.commit()
        print("   ✅ Cleanup completed")
    except Exception as e:
        print(f"   ⚠️ Cleanup failed: {e}")

async def main():
    """Run all security tests."""
    print("🔒 Memory Security Tests")
    print("=" * 40)
    
    test_results = []
    state_ids = []
    
    tests = [
        ("Content Sanitization", test_content_sanitization),
        ("Security Validation", test_security_validation),
        ("Access Control", test_access_control),
        ("Security Audit", test_security_audit),
        ("Strict Sanitization", test_strict_sanitization),
    ]
    
    for test_name, test_func in tests:
        try:
            success = await test_func()
            test_results.append((test_name, success))
        except Exception as e:
            print(f"   ❌ {test_name} failed with exception: {e}")
            test_results.append((test_name, False))
    
    # Summary
    print("\n📊 Security Test Results")
    print("=" * 40)
    passed = 0
    for test_name, success in test_results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        print("🎉 All security tests PASSED!")
        return True
    else:
        print("⚠️ Some security tests FAILED!")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
