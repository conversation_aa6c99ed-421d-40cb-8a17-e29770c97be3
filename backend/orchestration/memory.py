"""
A shared memory interface to enrich context across steps.
- Feeds data into `state.context`, including:
  -> Historical task traces.
  -> RAG-retrieved relevant information.
  -> Agent/system memory.
- Used by planner and agents to reason and personalize behavior.
"""

from __future__ import annotations

import uuid
import asyncio
from enum import Enum
from typing import Optional, List, Dict, Any, Union, Set, Tuple
from datetime import datetime, timezone, timedelta
from typing import TYPE_CHECKING

from pydantic import BaseModel, Field
import logging
import traceback
import re
import html
import json
from contextlib import asynccontextmanager

# Configure structured logging for memory operations
logger = logging.getLogger(__name__)

class MemoryOperationError(Exception):
    """Custom exception for memory operation failures."""
    def __init__(self, message: str, operation: str, context: Optional[Dict[str, Any]] = None):
        self.operation = operation
        self.context = context or {}
        super().__init__(message)

class DatabaseConnectionError(MemoryOperationError):
    """Exception for database connectivity issues."""
    pass

class MemoryValidationError(MemoryOperationError):
    """Exception for memory validation failures."""
    pass

class SecurityViolationError(MemoryOperationError):
    """Exception for security policy violations."""
    pass

class ContentSanitizer:
    """Handles content sanitization and security validation."""

    # Patterns for potentially sensitive information
    SENSITIVE_PATTERNS = {
        'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
        'phone': re.compile(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'),
        'ssn': re.compile(r'\b\d{3}-?\d{2}-?\d{4}\b'),
        'credit_card': re.compile(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'),
        'api_key': re.compile(r'\b[A-Za-z0-9]{32,}\b'),
        'password': re.compile(r'(?i)(password|passwd|pwd)\s*[:=]\s*\S+'),
        'token': re.compile(r'(?i)(token|bearer)\s*[:=]\s*[A-Za-z0-9._-]+'),
    }

    # HTML/Script injection patterns
    INJECTION_PATTERNS = {
        'script': re.compile(r'<script[^>]*>.*?</script>', re.IGNORECASE | re.DOTALL),
        'javascript': re.compile(r'javascript:', re.IGNORECASE),
        'on_events': re.compile(r'on\w+\s*=', re.IGNORECASE),
        'sql_injection': re.compile(r'(?i)(union|select|insert|update|delete|drop|create|alter)\s+', re.IGNORECASE),
    }

    @classmethod
    def sanitize_content(cls, content: str, strict_mode: bool = False) -> str:
        """
        Sanitize content for safe storage and display.

        Args:
            content: Raw content to sanitize
            strict_mode: If True, applies stricter sanitization

        Returns:
            Sanitized content
        """
        if not content:
            return content

        # HTML escape to prevent XSS
        sanitized = html.escape(content)

        # Remove or mask potentially sensitive information
        if strict_mode:
            for pattern_name, pattern in cls.SENSITIVE_PATTERNS.items():
                sanitized = pattern.sub(f'[REDACTED_{pattern_name.upper()}]', sanitized)

        # Remove dangerous script content
        for pattern_name, pattern in cls.INJECTION_PATTERNS.items():
            sanitized = pattern.sub('[REMOVED_UNSAFE_CONTENT]', sanitized)

        return sanitized

    @classmethod
    def validate_security(cls, content: str, agent: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Validate content for security violations.

        Args:
            content: Content to validate
            agent: Agent identifier
            metadata: Optional metadata to validate

        Raises:
            SecurityViolationError: If security violations are detected
        """
        violations = []

        # Check for injection attempts
        for pattern_name, pattern in cls.INJECTION_PATTERNS.items():
            if pattern.search(content):
                violations.append(f"Potential {pattern_name} injection detected")

        # Check agent identifier for suspicious patterns
        if not re.match(r'^[a-zA-Z0-9_-]+$', agent):
            violations.append("Agent identifier contains invalid characters")

        # Validate metadata if provided
        if metadata:
            try:
                # Ensure metadata is JSON serializable and doesn't contain dangerous content
                json_str = json.dumps(metadata)
                if len(json_str) > 10000:  # 10KB limit for metadata
                    violations.append("Metadata exceeds size limit")

                # Check for script injection in metadata values
                for key, value in metadata.items():
                    if isinstance(value, str):
                        for pattern_name, pattern in cls.INJECTION_PATTERNS.items():
                            if pattern.search(value):
                                violations.append(f"Potential {pattern_name} injection in metadata.{key}")
            except (TypeError, ValueError) as e:
                violations.append(f"Invalid metadata format: {e}")

        if violations:
            raise SecurityViolationError(
                f"Security violations detected: {'; '.join(violations)}",
                "security_validation",
                {"violations": violations, "agent": agent}
            )

@asynccontextmanager
async def memory_operation_context(operation: str, **context):
    """Context manager for memory operations with structured logging and error handling."""
    start_time = datetime.now()
    operation_id = str(uuid.uuid4())[:8]

    logger.info(f"[{operation_id}] Starting {operation}", extra={
        "operation": operation,
        "operation_id": operation_id,
        "context": context
    })

    try:
        yield operation_id
        duration = (datetime.now() - start_time).total_seconds()
        logger.info(f"[{operation_id}] Completed {operation} in {duration:.3f}s", extra={
            "operation": operation,
            "operation_id": operation_id,
            "duration": duration,
            "status": "success"
        })
    except Exception as e:
        duration = (datetime.now() - start_time).total_seconds()
        logger.error(f"[{operation_id}] Failed {operation} after {duration:.3f}s: {e}", extra={
            "operation": operation,
            "operation_id": operation_id,
            "duration": duration,
            "status": "error",
            "error": str(e),
            "traceback": traceback.format_exc()
        })
        raise

if TYPE_CHECKING:
    from .state import OrchestrationStep, LangGraphState
    from ..rag import KnowledgeBaseService

class MemoryUnits:
    """Represents a unit of memory in the orchestration system."""
    def __init__(
        self,
        memory_id: str,
        timestamp: datetime,
        current_step: 'OrchestrationStep',
        content: str,
        agent: str,
        task_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        embedding: Optional[List[float]] = None,
    ):
        self.memory_id = memory_id
        self.timestamp = timestamp
        self.current_step = current_step
        self.content = content
        self.agent = agent
        self.task_id = task_id
        self.metadata = metadata or {}
        self.tags = tags or []
        self.embedding = embedding

class GlobalMemory:
    """Global memory management for orchestration."""
    def __init__(
        self,
        knowledge_base: Optional['KnowledgeBaseService'] = None,
        cache_ttl: int = 300,  # 5 minutes cache TTL
        auto_persist: bool = True,  # Auto-persist to PostgreSQL
        session_state_id: Optional[str] = None  # Default state ID for session
    ):
        self.knowledge_base = knowledge_base
        self.memory_units: List[MemoryUnits] = []
        self._cache: Dict[str, Tuple[datetime, List[MemoryUnits]]] = {}
        self._cache_ttl = cache_ttl
        self.auto_persist = auto_persist
        self.session_state_id = session_state_id
        self._session_initialized = False
        self._connection_healthy = True
        self._last_health_check = None
        self._security_enabled = True
        self._strict_sanitization = False
        self._allowed_agents = set()  # Empty set means all agents allowed
        self._content_sanitizer = ContentSanitizer()

    def _generate_cache_key(self, **kwargs) -> str:
        """
        Generate a deterministic hash-based cache key.

        Args:
            **kwargs: Key-value pairs to include in cache key

        Returns:
            Deterministic cache key string
        """
        import hashlib
        import json

        # Sort keys for deterministic hashing
        sorted_params = dict(sorted(kwargs.items()))

        # Convert to JSON string with sorted keys
        key_string = json.dumps(sorted_params, sort_keys=True, default=str)

        # Generate hash
        hash_obj = hashlib.sha256(key_string.encode())
        return f"mem_cache_{hash_obj.hexdigest()[:16]}"

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached results are still valid."""
        if cache_key not in self._cache:
            return False
        timestamp, _ = self._cache[cache_key]
        return (datetime.now() - timestamp).total_seconds() < self._cache_ttl

    def _validate_memory_input(self, content: str, agent: str, current_step: 'OrchestrationStep') -> None:
        """
        Validate memory input parameters.

        Args:
            content: Memory content
            agent: Agent identifier
            current_step: Orchestration step

        Raises:
            MemoryValidationError: If validation fails
        """
        if not content or not content.strip():
            raise MemoryValidationError("Memory content cannot be empty", "validation")

        if len(content) > 100000:  # 100KB limit
            raise MemoryValidationError("Memory content exceeds maximum size (100KB)", "validation")

        if not agent or not agent.strip():
            raise MemoryValidationError("Agent identifier cannot be empty", "validation")

        if len(agent) > 64:
            raise MemoryValidationError("Agent identifier exceeds maximum length (64 chars)", "validation")

        if current_step is None:
            raise MemoryValidationError("Orchestration step cannot be None", "validation")

    def configure_security(
        self,
        enabled: bool = True,
        strict_sanitization: bool = False,
        allowed_agents: Optional[List[str]] = None
    ) -> None:
        """
        Configure security settings for the memory system.

        Args:
            enabled: Enable/disable security features
            strict_sanitization: Enable strict content sanitization
            allowed_agents: List of allowed agent identifiers (None = all allowed)
        """
        self._security_enabled = enabled
        self._strict_sanitization = strict_sanitization

        if allowed_agents is not None:
            self._allowed_agents = set(allowed_agents)
        else:
            self._allowed_agents = set()

        logger.info(f"Security configured: enabled={enabled}, strict={strict_sanitization}, "
                   f"allowed_agents={len(self._allowed_agents) if self._allowed_agents else 'all'}")

    def _validate_agent_access(self, agent: str) -> None:
        """
        Validate agent access permissions.

        Args:
            agent: Agent identifier to validate

        Raises:
            SecurityViolationError: If agent access is denied
        """
        if not self._security_enabled:
            return

        # Check if agent is in allowed list (empty set means all allowed)
        if self._allowed_agents and agent not in self._allowed_agents:
            raise SecurityViolationError(
                f"Agent '{agent}' is not authorized to access memory system",
                "agent_authorization",
                {"agent": agent, "allowed_agents": list(self._allowed_agents)}
            )

    def _sanitize_and_validate(
        self,
        content: str,
        agent: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Sanitize content and validate for security violations.

        Args:
            content: Content to sanitize and validate
            agent: Agent identifier
            metadata: Optional metadata to validate

        Returns:
            Sanitized content

        Raises:
            SecurityViolationError: If security violations are detected
        """
        if not self._security_enabled:
            return content

        # Validate security first
        self._content_sanitizer.validate_security(content, agent, metadata)

        # Sanitize content
        sanitized_content = self._content_sanitizer.sanitize_content(
            content,
            strict_mode=self._strict_sanitization
        )

        return sanitized_content

    async def _check_database_health(self) -> bool:
        """
        Check database connectivity and health.

        Returns:
            True if database is healthy, False otherwise
        """
        try:
            from backend.app.core.db.database import get_async_db_session
            from sqlalchemy import text

            async with get_async_db_session() as db:
                await db.execute(text("SELECT 1"))
                self._connection_healthy = True
                self._last_health_check = datetime.now()
                return True

        except Exception as e:
            self._connection_healthy = False
            self._last_health_check = datetime.now()
            logger.error(f"Database health check failed: {e}")
            return False

    async def _with_database_fallback(self, operation_func, fallback_func=None, operation_name: str = "database_operation"):
        """
        Execute database operation with fallback handling.

        Args:
            operation_func: Primary database operation function
            fallback_func: Optional fallback function
            operation_name: Name of the operation for logging

        Returns:
            Result of operation or fallback
        """
        try:
            # Check database health if it's been a while
            if (not self._last_health_check or
                (datetime.now() - self._last_health_check).total_seconds() > 300):  # 5 minutes
                await self._check_database_health()

            if not self._connection_healthy:
                raise DatabaseConnectionError("Database connection is unhealthy", operation_name)

            return await operation_func()

        except DatabaseConnectionError:
            raise
        except Exception as e:
            logger.error(f"Database operation '{operation_name}' failed: {e}")

            # Mark connection as unhealthy
            self._connection_healthy = False

            if fallback_func:
                logger.info(f"Using fallback for operation '{operation_name}'")
                return await fallback_func()
            else:
                raise MemoryOperationError(f"Database operation failed: {e}", operation_name)

    async def _get_database_version(self, state_id: Optional[str] = None) -> str:
        """
        Get a version identifier for the database state to detect external changes.

        Args:
            state_id: Optional state ID to scope the version check

        Returns:
            Version string representing current database state
        """
        try:
            from backend.app.core.db.database import get_async_db_session
            from sqlalchemy import text

            async with get_async_db_session() as db:
                if state_id:
                    # Get count and latest timestamp for specific state
                    result = await db.execute(
                        text("SELECT COUNT(*), MAX(created_at) FROM orchestration_memory WHERE state_id = :state_id"),
                        {"state_id": state_id}
                    )
                else:
                    # Get global count and latest timestamp
                    result = await db.execute(
                        text("SELECT COUNT(*), MAX(created_at) FROM orchestration_memory")
                    )

                count, latest_timestamp = result.fetchone()

                # Create version string from count and timestamp
                timestamp_str = latest_timestamp.isoformat() if latest_timestamp else "none"
                return f"{count}_{timestamp_str}"

        except Exception as e:
            logger.error(f"Failed to get database version: {e}")
            return "unknown"

    async def initialize_session(self, state_id: str, load_existing: bool = True) -> bool:
        """
        Initialize a memory session for cross-session persistence.

        Args:
            state_id: State ID to associate with this session
            load_existing: Whether to load existing memories from PostgreSQL

        Returns:
            True if initialization was successful
        """
        try:
            self.session_state_id = state_id

            if load_existing:
                # Load existing memories from PostgreSQL
                existing_memories = await self.retrieve_memory(
                    state_id=state_id,
                    limit=1000,  # Load a reasonable number of recent memories
                    force_refresh=True
                )

                # Clear in-memory storage and reload from database
                self.memory_units.clear()
                self.memory_units.extend(existing_memories)

                logger.info(f"Initialized memory session {state_id} with {len(existing_memories)} existing memories")

            self._session_initialized = True
            return True

        except Exception as e:
            logger.error(f"Failed to initialize memory session {state_id}: {e}")
            return False

    async def persist_session(self, state_id: Optional[str] = None) -> bool:
        """
        Persist all in-memory memories to PostgreSQL.

        Args:
            state_id: Optional state ID (uses session_state_id if not provided)

        Returns:
            True if persistence was successful
        """
        try:
            target_state_id = state_id or self.session_state_id
            if not target_state_id:
                logger.warning("No state_id provided for session persistence")
                return False

            persisted_count = 0

            for memory in self.memory_units:
                try:
                    # Check if memory is already persisted (has a valid UUID)
                    if not memory.memory_id or len(memory.memory_id) < 10:
                        from backend.orchestration.db import save_memory
                        memory_id = await save_memory(memory, target_state_id, memory.embedding)
                        memory.memory_id = memory_id
                        persisted_count += 1
                except Exception as e:
                    logger.error(f"Failed to persist memory {memory.memory_id}: {e}")

            logger.info(f"Persisted {persisted_count} memories to PostgreSQL for state {target_state_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to persist session: {e}")
            return False

    async def add_memory(
        self,
        content: str,
        agent: str,
        current_step: 'OrchestrationStep',
        task_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        embedding: Optional[List[float]] = None,
        state_id: Optional[str] = None,
    ) -> str:
        """
        Add a new memory unit to the global memory.
        Automatically persists to PostgreSQL if auto_persist is enabled or state_id is provided.
        """
        async with memory_operation_context("add_memory",
                                           agent=agent,
                                           state_id=state_id or self.session_state_id,
                                           content_length=len(content) if content else 0):

            # Validate input
            self._validate_memory_input(content, agent, current_step)

            # Security validation and access control
            self._validate_agent_access(agent)

            # Sanitize content for security
            sanitized_content = self._sanitize_and_validate(content, agent, metadata)

            memory = MemoryUnits(
                memory_id=str(uuid.uuid4()),
                timestamp=datetime.now(timezone.utc),
                current_step=current_step,
                content=sanitized_content,  # Use sanitized content
                agent=agent,
                task_id=task_id,
                metadata=metadata,
                tags=tags,
                embedding=embedding
            )

            # Use provided state_id or session default
            target_state_id = state_id or self.session_state_id

            # Auto-persist to PostgreSQL if enabled and state_id available
            if (self.auto_persist or state_id) and target_state_id:

                async def persist_operation():
                    from backend.orchestration.db import save_memory
                    memory_id = await save_memory(memory, target_state_id, embedding)
                    memory.memory_id = memory_id
                    logger.debug(f"Persisted memory {memory_id} to PostgreSQL for state {target_state_id}")
                    return memory_id

                async def fallback_operation():
                    logger.warning(f"Database persistence failed, storing memory {memory.memory_id} in-memory only")
                    return memory.memory_id

                try:
                    await self._with_database_fallback(persist_operation, fallback_operation, "persist_memory")
                except Exception as e:
                    logger.error(f"Failed to persist memory, continuing with in-memory storage: {e}")

            self.memory_units.append(memory)

            # Invalidate relevant caches
            self._invalidate_caches(agent, task_id, tags, target_state_id)

            return memory.memory_id

    def _invalidate_caches(self, agent: Optional[str] = None, task_id: Optional[str] = None, tags: Optional[List[str]] = None, state_id: Optional[str] = None):
        """
        Invalidate caches based on memory changes using intelligent cache key matching.

        Args:
            agent: Agent that was modified
            task_id: Task ID that was modified
            tags: Tags that were modified
            state_id: State ID that was modified
        """
        cache_keys_to_invalidate = []

        # For hash-based cache keys, we need to be more aggressive with invalidation
        # since we can't easily parse the hash to determine what it contains

        if agent or task_id or tags or state_id:
            # If any specific parameters changed, invalidate all caches
            # This is conservative but ensures consistency
            cache_keys_to_invalidate = list(self._cache.keys())

        for key in cache_keys_to_invalidate:
            del self._cache[key]

        logger.debug(f"Invalidated {len(cache_keys_to_invalidate)} cache entries")

    async def invalidate_external_changes(self, state_id: Optional[str] = None) -> bool:
        """
        Check for and invalidate caches if external database changes are detected.

        Args:
            state_id: Optional state ID to check for changes

        Returns:
            True if changes were detected and caches invalidated
        """
        try:
            current_version = await self._get_database_version(state_id)
            cache_key = f"db_version_{state_id or 'global'}"

            if cache_key in self._cache:
                _, stored_version = self._cache[cache_key]

                if stored_version != current_version:
                    # Database has changed externally, invalidate all caches
                    self._cache.clear()
                    logger.info(f"Detected external database changes, invalidated all caches")

                    # Update version cache
                    self._cache[cache_key] = (datetime.now(), current_version)
                    return True
            else:
                # First time checking, store current version
                self._cache[cache_key] = (datetime.now(), current_version)

            return False

        except Exception as e:
            logger.error(f"Failed to check for external changes: {e}")
            return False

    async def search_memory(
        self,
        query: str,
        state_id: Optional[str] = None,
        limit: int = 10,
        search_type: str = "hybrid"  # "vector", "text", "hybrid"
    ) -> List[MemoryUnits]:
        """
        Comprehensive memory search using PostgreSQL and vector similarity.

        Args:
            query: Search query
            state_id: Optional state ID to filter by
            limit: Maximum number of results
            search_type: Type of search ("vector", "text", "hybrid")

        Returns:
            List of matching memory units
        """
        try:
            results = []

            if search_type in ["vector", "hybrid"]:
                # Vector similarity search
                try:
                    from backend.orchestration.db import search_memory_by_vector
                    from backend.rag.embeddings import get_embedding_model

                    # Generate embedding for query
                    embedding_model = get_embedding_model()
                    query_embedding = await embedding_model.embed_query(query)

                    # Search by vector similarity
                    vector_results = await search_memory_by_vector(query_embedding, limit=limit)

                    # Convert search results to MemoryUnits
                    for result in vector_results:
                        doc = result["document"]
                        from backend.orchestration.state import OrchestrationStep

                        memory = MemoryUnits(
                            memory_id=doc["id"],
                            timestamp=datetime.fromisoformat(doc["metadata"]["created_at"]) if doc["metadata"].get("created_at") else datetime.now(),
                            current_step=OrchestrationStep(doc["metadata"]["current_step"]),
                            content=doc["content"],
                            agent=doc["metadata"]["agent"],
                            task_id=doc["metadata"]["task_id"],
                            metadata=doc["metadata"].get("memory_metadata", {}),
                            tags=doc["metadata"].get("tags", []),
                            embedding=None
                        )
                        results.append(memory)

                    logger.debug(f"Vector search returned {len(vector_results)} results")
                except Exception as e:
                    logger.error(f"Vector search failed: {e}")

            # Filter by state_id if provided
            if state_id:
                results = [m for m in results if hasattr(m, 'state_id') and m.state_id == state_id]

            # Sort by relevance/timestamp and limit
            results.sort(key=lambda x: x.timestamp, reverse=True)
            return results[:limit]

        except Exception as e:
            logger.error(f"Memory search failed: {e}")
            return []

    async def delete_memory(self, memory_id: str, state_id: Optional[str] = None) -> bool:
        """
        Delete a memory unit from PostgreSQL and in-memory storage.

        Args:
            memory_id: ID of the memory to delete
            state_id: Optional state ID for additional validation

        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            # Delete from PostgreSQL
            from backend.app.core.db.database import get_async_db_session
            from sqlalchemy import text

            async with get_async_db_session() as db:
                if state_id:
                    result = await db.execute(
                        text("DELETE FROM orchestration_memory WHERE id = :memory_id AND state_id = :state_id"),
                        {"memory_id": memory_id, "state_id": state_id}
                    )
                else:
                    result = await db.execute(
                        text("DELETE FROM orchestration_memory WHERE id = :memory_id"),
                        {"memory_id": memory_id}
                    )
                await db.commit()

                deleted_count = result.rowcount
                logger.debug(f"Deleted {deleted_count} memory records from PostgreSQL")

            # Delete from in-memory storage
            self.memory_units = [m for m in self.memory_units if m.memory_id != memory_id]

            # Invalidate caches
            self._cache.clear()

            return deleted_count > 0

        except Exception as e:
            logger.error(f"Failed to delete memory {memory_id}: {e}")
            return False

    async def update_memory(
        self,
        memory_id: str,
        content: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        state_id: Optional[str] = None
    ) -> bool:
        """
        Update a memory unit in PostgreSQL and in-memory storage.

        Args:
            memory_id: ID of the memory to update
            content: New content (optional)
            metadata: New metadata (optional)
            tags: New tags (optional)
            state_id: Optional state ID for additional validation

        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Update in PostgreSQL
            from backend.app.core.db.database import get_async_db_session
            from sqlalchemy import text
            import json

            update_fields = []
            params = {"memory_id": memory_id}

            if content is not None:
                update_fields.append("content = :content")
                params["content"] = content

            if metadata is not None:
                update_fields.append("memory_metadata = :metadata")
                params["metadata"] = json.dumps(metadata)

            if tags is not None:
                update_fields.append("tags = :tags")
                params["tags"] = json.dumps(tags)

            if not update_fields:
                return True  # Nothing to update

            query = f"UPDATE orchestration_memory SET {', '.join(update_fields)} WHERE id = :memory_id"
            if state_id:
                query += " AND state_id = :state_id"
                params["state_id"] = state_id

            async with get_async_db_session() as db:
                result = await db.execute(text(query), params)
                await db.commit()

                updated_count = result.rowcount
                logger.debug(f"Updated {updated_count} memory records in PostgreSQL")

            # Update in-memory storage
            for memory in self.memory_units:
                if memory.memory_id == memory_id:
                    if content is not None:
                        memory.content = content
                    if metadata is not None:
                        memory.metadata = metadata
                    if tags is not None:
                        memory.tags = tags
                    break

            # Invalidate caches
            self._cache.clear()

            return updated_count > 0

        except Exception as e:
            logger.error(f"Failed to update memory {memory_id}: {e}")
            return False

    async def retrieve_memory(
        self,
        query: Optional[str] = None,
        state_id: Optional[str] = None,
        limit: int = 10,
        agent: Optional[str] = None,
        task_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        force_refresh: bool = False
    ) -> List[MemoryUnits]:
        """
        Retrieve memory units with advanced filtering and caching.

        Args:
            query: Optional search query for semantic search
            state_id: Optional state ID for database retrieval
            limit: Maximum number of results to return
            agent: Filter by agent
            task_id: Filter by task ID
            tags: Filter by tags
            force_refresh: Force cache refresh

        Returns:
            List of matching memory units
        """
        async with memory_operation_context("retrieve_memory",
                                           query=query,
                                           state_id=state_id,
                                           limit=limit,
                                           agent=agent,
                                           task_id=task_id,
                                           tags=tags):

            # Validate inputs
            if limit <= 0:
                raise MemoryValidationError("Limit must be positive", "retrieve_memory")
            if limit > 1000:
                logger.warning(f"Large limit requested: {limit}, capping at 1000")
                limit = 1000

            # Security validation for agent access
            if agent:
                self._validate_agent_access(agent)

            # Generate hash-based cache key
            cache_key = self._generate_cache_key(
                query=query,
                state_id=state_id,
                agent=agent,
                task_id=task_id,
                tags=tags,
                limit=limit
            )

            # Check for external database changes first
            if not force_refresh:
                await self.invalidate_external_changes(state_id)

            # Check cache first
            if not force_refresh and self._is_cache_valid(cache_key):
                cached_results = self._cache[cache_key][1]
                logger.debug(f"Retrieved {len(cached_results)} memories from cache")
                return cached_results

            results = []

            # Primary retrieval: PostgreSQL database with fallback
            if state_id:
                async def db_retrieval():
                    from backend.orchestration.db import load_memory
                    db_memories = await load_memory(state_id, limit=limit * 2)  # Get more for filtering
                    logger.debug(f"Retrieved {len(db_memories)} memories from PostgreSQL")
                    return db_memories

                async def db_fallback():
                    logger.warning("Database retrieval failed, using in-memory fallback")
                    return []

                try:
                    db_results = await self._with_database_fallback(db_retrieval, db_fallback, "load_memory")
                    results.extend(db_results)
                except Exception as e:
                    logger.error(f"Database retrieval failed completely: {e}")

            # Secondary retrieval: Vector search if query provided
            if query and len(results) < limit:
                async def vector_search():
                    from backend.orchestration.db import search_memory_by_vector
                    from backend.rag.embeddings import get_embedding_model

                    # Generate embedding for query
                    embedding_model = get_embedding_model()
                    query_embedding = await embedding_model.embed_query(query)

                    # Search by vector similarity
                    vector_results = await search_memory_by_vector(query_embedding, limit=limit)

                    # Convert search results to MemoryUnits
                    converted_results = []
                    for result in vector_results:
                        doc = result["document"]
                        # Import here to avoid circular imports
                        from backend.orchestration.state import OrchestrationStep

                        memory = MemoryUnits(
                            memory_id=doc["id"],
                            timestamp=datetime.fromisoformat(doc["metadata"]["created_at"]) if doc["metadata"].get("created_at") else datetime.now(),
                            current_step=OrchestrationStep(doc["metadata"]["current_step"]),
                            content=doc["content"],
                            agent=doc["metadata"]["agent"],
                            task_id=doc["metadata"]["task_id"],
                            metadata=doc["metadata"].get("memory_metadata", {}),
                            tags=doc["metadata"].get("tags", []),
                            embedding=None  # Don't load full embedding
                        )
                        converted_results.append(memory)

                    logger.debug(f"Retrieved {len(vector_results)} memories from vector search")
                    return converted_results

                try:
                    vector_results = await vector_search()
                    results.extend(vector_results)
                except Exception as e:
                    logger.error(f"Vector search failed: {e}")

            # Fallback: In-memory storage (for backwards compatibility)
            if not results and self.memory_units:
                results = self.memory_units.copy()
                logger.debug(f"Using {len(results)} in-memory memories as fallback")

            # Apply filters
            if agent:
                results = [m for m in results if m.agent == agent]
            if task_id:
                results = [m for m in results if m.task_id == task_id]
            if tags:
                results = [m for m in results if any(tag in m.tags for tag in tags)]

            # Sort by timestamp and limit
            results.sort(key=lambda x: x.timestamp, reverse=True)
            results = results[:limit]

            # Update cache
            self._cache[cache_key] = (datetime.now(), results)

            logger.debug(f"Retrieved {len(results)} total memories after filtering")
            return results

    async def inject_into_context(self, state: 'LangGraphState') -> 'LangGraphState':
        """
        Inject relevant memory into the LangGraphState context.

        Args:
            state: Current LangGraphState

        Returns:
            Updated LangGraphState with memory context injected
        """
        try:
            # Get recent memories for context from PostgreSQL
            recent_memories = await self.retrieve_memory(
                state_id=state.id if hasattr(state, 'id') else None,
                limit=10,
                query=None
            )

            # Format memories for context
            memory_context = []
            for memory in recent_memories:
                memory_context.append({
                    "timestamp": memory.timestamp.isoformat(),
                    "agent": memory.agent,
                    "task_id": memory.task_id,
                    "content": memory.content[:200] + "..." if len(memory.content) > 200 else memory.content,
                    "tags": memory.tags,
                    "metadata": memory.metadata
                })

            # Inject into state context
            if "memory_context" not in state.context:
                state.context["memory_context"] = []

            state.context["memory_context"] = memory_context

            logger.debug(f"[Memory] Injected {len(memory_context)} memories into state context")
            return state
        except Exception as e:
            logger.error(f"Failed to inject memory into context: {e}")
            return state

    async def get_memory_statistics(self, state_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get comprehensive memory statistics from PostgreSQL.

        Args:
            state_id: Optional state ID to filter statistics

        Returns:
            Dictionary containing memory statistics
        """
        try:
            from backend.app.core.db.database import get_async_db_session
            from sqlalchemy import text

            stats = {
                "total_memories": 0,
                "memories_by_agent": {},
                "memories_by_step": {},
                "memories_with_embeddings": 0,
                "average_content_length": 0,
                "oldest_memory": None,
                "newest_memory": None,
                "cache_size": len(self._cache),
                "in_memory_count": len(self.memory_units)
            }

            async with get_async_db_session() as db:
                # Basic counts
                base_query = "FROM orchestration_memory"
                where_clause = ""
                params = {}

                if state_id:
                    where_clause = " WHERE state_id = :state_id"
                    params["state_id"] = state_id

                # Total count
                result = await db.execute(
                    text(f"SELECT COUNT(*) {base_query}{where_clause}"),
                    params
                )
                stats["total_memories"] = result.scalar()

                # Count with embeddings
                embedding_where = " AND embedding IS NOT NULL" if where_clause else " WHERE embedding IS NOT NULL"
                result = await db.execute(
                    text(f"SELECT COUNT(*) {base_query}{where_clause}{embedding_where}"),
                    params
                )
                stats["memories_with_embeddings"] = result.scalar()

                # Average content length
                result = await db.execute(
                    text(f"SELECT AVG(LENGTH(content)) {base_query}{where_clause}"),
                    params
                )
                avg_length = result.scalar()
                stats["average_content_length"] = float(avg_length) if avg_length else 0

                # Memories by agent
                result = await db.execute(
                    text(f"SELECT agent, COUNT(*) {base_query}{where_clause} GROUP BY agent"),
                    params
                )
                stats["memories_by_agent"] = dict(result.fetchall())

                # Memories by orchestration step
                result = await db.execute(
                    text(f"SELECT current_step, COUNT(*) {base_query}{where_clause} GROUP BY current_step"),
                    params
                )
                stats["memories_by_step"] = dict(result.fetchall())

                # Oldest and newest memories
                result = await db.execute(
                    text(f"SELECT MIN(created_at), MAX(created_at) {base_query}{where_clause}"),
                    params
                )
                oldest, newest = result.fetchone()
                stats["oldest_memory"] = oldest.isoformat() if oldest else None
                stats["newest_memory"] = newest.isoformat() if newest else None

            logger.debug(f"Generated memory statistics: {stats['total_memories']} total memories")
            return stats

        except Exception as e:
            logger.error(f"Failed to get memory statistics: {e}")
            return {"error": str(e)}

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the memory system.

        Returns:
            Dictionary containing health check results
        """
        try:
            health = {
                "status": "healthy",
                "checks": {},
                "errors": [],
                "warnings": []
            }

            # Check PostgreSQL connectivity
            try:
                from backend.app.core.db.database import get_async_db_session
                from sqlalchemy import text

                async with get_async_db_session() as db:
                    await db.execute(text("SELECT 1"))
                health["checks"]["postgresql_connection"] = "pass"
            except Exception as e:
                health["checks"]["postgresql_connection"] = "fail"
                health["errors"].append(f"PostgreSQL connection failed: {e}")
                health["status"] = "unhealthy"

            # Check memory table accessibility
            try:
                stats = await self.get_memory_statistics()
                if "error" in stats:
                    health["checks"]["memory_table_access"] = "fail"
                    health["errors"].append(f"Memory table access failed: {stats['error']}")
                    health["status"] = "unhealthy"
                else:
                    health["checks"]["memory_table_access"] = "pass"
                    health["checks"]["total_memories"] = stats["total_memories"]
            except Exception as e:
                health["checks"]["memory_table_access"] = "fail"
                health["errors"].append(f"Memory table access failed: {e}")
                health["status"] = "unhealthy"

            # Check cache health
            cache_size = len(self._cache)
            health["checks"]["cache_size"] = cache_size
            if cache_size > 1000:  # Arbitrary threshold
                health["warnings"].append(f"Large cache size: {cache_size} entries")
                if health["status"] == "healthy":
                    health["status"] = "degraded"

            # Check in-memory storage
            memory_count = len(self.memory_units)
            health["checks"]["in_memory_count"] = memory_count
            if memory_count > 10000:  # Arbitrary threshold
                health["warnings"].append(f"Large in-memory storage: {memory_count} entries")
                if health["status"] == "healthy":
                    health["status"] = "degraded"

            return health

        except Exception as e:
            return {
                "status": "unhealthy",
                "checks": {},
                "errors": [f"Health check failed: {e}"],
                "warnings": []
            }

    async def security_audit(self, state_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Perform a security audit of stored memories.

        Args:
            state_id: Optional state ID to scope the audit

        Returns:
            Dictionary containing security audit results
        """
        try:
            audit_results = {
                "status": "secure",
                "checks": {},
                "violations": [],
                "warnings": [],
                "recommendations": []
            }

            # Get all memories for audit
            memories = await self.retrieve_memory(state_id=state_id, limit=1000)

            # Check for potential sensitive information
            sensitive_count = 0
            large_content_count = 0

            for memory in memories:
                # Check content size
                if len(memory.content) > 50000:  # 50KB threshold
                    large_content_count += 1

                # Check for sensitive patterns
                for pattern_name, pattern in ContentSanitizer.SENSITIVE_PATTERNS.items():
                    if pattern.search(memory.content):
                        sensitive_count += 1
                        audit_results["violations"].append({
                            "memory_id": memory.memory_id,
                            "type": f"potential_{pattern_name}",
                            "agent": memory.agent,
                            "timestamp": memory.timestamp.isoformat()
                        })
                        break

            # Security configuration check
            audit_results["checks"]["security_enabled"] = self._security_enabled
            audit_results["checks"]["strict_sanitization"] = self._strict_sanitization
            audit_results["checks"]["agent_restrictions"] = len(self._allowed_agents) > 0
            audit_results["checks"]["total_memories_audited"] = len(memories)
            audit_results["checks"]["sensitive_content_detected"] = sensitive_count
            audit_results["checks"]["large_content_count"] = large_content_count

            # Generate warnings and recommendations
            if not self._security_enabled:
                audit_results["warnings"].append("Security features are disabled")
                audit_results["recommendations"].append("Enable security features with configure_security()")

            if sensitive_count > 0:
                audit_results["warnings"].append(f"Found {sensitive_count} memories with potentially sensitive content")
                audit_results["recommendations"].append("Enable strict sanitization mode")
                audit_results["status"] = "warning"

            if large_content_count > 0:
                audit_results["warnings"].append(f"Found {large_content_count} memories with large content")
                audit_results["recommendations"].append("Consider content size limits")

            if len(self._allowed_agents) == 0 and self._security_enabled:
                audit_results["warnings"].append("No agent access restrictions configured")
                audit_results["recommendations"].append("Configure allowed agents for better security")

            # Set overall status
            if len(audit_results["violations"]) > 0:
                audit_results["status"] = "violations_detected"
            elif len(audit_results["warnings"]) > 0 and audit_results["status"] == "secure":
                audit_results["status"] = "warnings"

            return audit_results

        except Exception as e:
            return {
                "status": "audit_failed",
                "error": str(e),
                "checks": {},
                "violations": [],
                "warnings": [],
                "recommendations": ["Fix audit system errors before proceeding"]
            }