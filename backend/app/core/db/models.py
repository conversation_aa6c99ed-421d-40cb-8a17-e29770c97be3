"""
Database Models

This module defines SQLAlchemy models for the application database.
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List

from sqlalchemy import Column, String, Text, DateTime, ForeignKey, JSON, Enum as SQLEnum, CheckConstraint, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

# Create base class for all models
Base = declarative_base()


class Document(Base):
    """
    Document model for storing document metadata.

    This model stores high-level document information including title,
    source, and metadata. The actual document content is stored in
    chunks in the DocumentChunk model.
    """
    __tablename__ = "documents"

    id = Column(String(64), primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(Text, nullable=False)
    source = Column(Text, nullable=True)
    doc_metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship to document chunks
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<Document(id='{self.id}', title='{self.title[:50]}...')>"

    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary representation."""
        return {
            "id": self.id,
            "title": self.title,
            "source": self.source,
            "metadata": self.doc_metadata or {},
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "chunk_count": len(self.chunks) if self.chunks else 0
        }


class DocumentChunk(Base):
    """
    Document chunk model for storing document content with embeddings.

    This model stores individual chunks of document content along with
    their vector embeddings for similarity search.
    """
    __tablename__ = "document_chunks"

    id = Column(String(64), primary_key=True, default=lambda: str(uuid.uuid4()))
    document_id = Column(String(64), ForeignKey("documents.id", ondelete="CASCADE"), nullable=False)
    content = Column(Text, nullable=False)
    chunk_metadata = Column(JSON, nullable=True)
    # Note: embedding column is created via raw SQL in database.py due to vector type
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship to parent document
    document = relationship("Document", back_populates="chunks")

    def __repr__(self) -> str:
        return f"<DocumentChunk(id='{self.id}', content='{self.content[:50]}...')>"

    def to_dict(self, include_embedding: bool = False) -> Dict[str, Any]:
        """
        Convert document chunk to dictionary representation.

        Args:
            include_embedding: Whether to include the embedding vector

        Returns:
            Dictionary representation of the chunk
        """
        result = {
            "id": self.id,
            "document_id": self.document_id,
            "content": self.content,
            "metadata": self.chunk_metadata or {},
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

        # Note: embedding would need to be fetched separately via raw SQL
        # due to the vector type not being directly supported by SQLAlchemy

        return result


class OrchestrationState(Base):
    """
    Model for storing LangGraphState snapshots.
    """
    __tablename__ = "orchestration_states"

    id = Column(String(64), primary_key=True, nullable=False, default=lambda: str(uuid.uuid4()))
    user_input = Column(Text, nullable=False)
    context = Column(JSON, nullable=True)
    coordinator_plan_id = Column(String(64), nullable=True)
    current_step = Column(String(32), nullable=False)  # OrchestrationStep enum value
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)

    # Table-level constraints
    __table_args__ = (
        CheckConstraint(
            "current_step IN ('PLANNING', 'TASK_ASSIGNMENT', 'TASK_EXECUTION', 'REVIEW', 'DONE')",
            name='ck_orchestration_states_current_step'
        ),
        CheckConstraint(
            "length(user_input) > 0",
            name='ck_orchestration_states_user_input_not_empty'
        ),
        Index('idx_orchestration_states_current_step', 'current_step'),
        Index('idx_orchestration_states_created_at', 'created_at'),
    )

    # Relationships
    tasks = relationship("OrchestrationTask", back_populates="state", cascade="all, delete-orphan")
    memory_units = relationship("OrchestrationMemory", back_populates="state", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<OrchestrationState(id='{self.id}', step='{self.current_step}')>"

    def to_dict(self) -> Dict[str, Any]:
        """Convert state to dictionary representation."""
        return {
            "id": self.id,
            "user_input": self.user_input,
            "context": self.context or {},
            "coordinator_plan_id": self.coordinator_plan_id,
            "current_step": self.current_step,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "tasks": [task.to_dict() for task in self.tasks],
            "memory_units": [memory.to_dict() for memory in self.memory_units]
        }


class OrchestrationTask(Base):
    """
    Model for storing Task objects.
    """
    __tablename__ = "orchestration_tasks"

    id = Column(String(64), primary_key=True, nullable=False, default=lambda: str(uuid.uuid4()))
    state_id = Column(String(64), ForeignKey("orchestration_states.id", ondelete="CASCADE"), nullable=False)
    description = Column(Text, nullable=False)
    task_type = Column(String(32), nullable=False)  # TaskType enum value
    status = Column(String(32), nullable=False)  # TaskStatus enum value
    assigned_agent = Column(String(64), nullable=True)
    output = Column(JSON, nullable=True)
    task_metadata = Column(JSON, nullable=True)  # Renamed from metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)

    # Table-level constraints
    __table_args__ = (
        CheckConstraint(
            "task_type IN ('QUERY_DECOMPOSITION', 'REASONING', 'COMMUNICATION', 'TOOLS', 'ACTION', 'EVALUATION')",
            name='ck_orchestration_tasks_task_type'
        ),
        CheckConstraint(
            "status IN ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED')",
            name='ck_orchestration_tasks_status'
        ),
        CheckConstraint(
            "length(description) > 0",
            name='ck_orchestration_tasks_description_not_empty'
        ),
        Index('idx_orchestration_tasks_state_id', 'state_id'),
        Index('idx_orchestration_tasks_status', 'status'),
        Index('idx_orchestration_tasks_task_type', 'task_type'),
        Index('idx_orchestration_tasks_assigned_agent', 'assigned_agent'),
        Index('idx_orchestration_tasks_state_status', 'state_id', 'status'),
    )

    # Relationships
    state = relationship("OrchestrationState", back_populates="tasks")
    dependencies = relationship(
        "OrchestrationTask",
        secondary="task_dependencies",
        primaryjoin="OrchestrationTask.id==TaskDependency.task_id",
        secondaryjoin="OrchestrationTask.id==TaskDependency.dependency_id",
        backref="dependent_tasks"
    )

    def __repr__(self) -> str:
        return f"<OrchestrationTask(id='{self.id}', type='{self.task_type}', status='{self.status}')>"

    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary representation."""
        return {
            "id": self.id,
            "state_id": self.state_id,
            "description": self.description,
            "task_type": self.task_type,
            "status": self.status,
            "assigned_agent": self.assigned_agent,
            "output": self.output,
            "metadata": self.task_metadata or {},  # Map back to metadata in dict
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "dependencies": [dep.id for dep in self.dependencies]
        }


class TaskDependency(Base):
    """
    Association table for task dependencies.
    """
    __tablename__ = "task_dependencies"

    task_id = Column(String(64), ForeignKey("orchestration_tasks.id", ondelete="CASCADE"), primary_key=True, nullable=False)
    dependency_id = Column(String(64), ForeignKey("orchestration_tasks.id", ondelete="CASCADE"), primary_key=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Table-level constraints
    __table_args__ = (
        CheckConstraint('task_id != dependency_id', name='ck_task_dependencies_no_self_reference'),
        Index('idx_task_dependencies_dependency_id', 'dependency_id'),
    )


class OrchestrationMemory(Base):
    """
    Model for storing MemoryUnits.
    """
    __tablename__ = "orchestration_memory"

    id = Column(String(64), primary_key=True, nullable=False, default=lambda: str(uuid.uuid4()))
    state_id = Column(String(64), ForeignKey("orchestration_states.id", ondelete="CASCADE"), nullable=False)
    content = Column(Text, nullable=False)
    agent = Column(String(64), nullable=False)
    task_id = Column(String(64), nullable=True)
    current_step = Column(String(32), nullable=False)  # OrchestrationStep enum value
    memory_metadata = Column(JSON, nullable=True)  # Renamed from metadata
    tags = Column(JSON, nullable=True)  # Store tags as JSON array
    # embedding column is created via raw SQL in migration (VECTOR(768))
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Table-level constraints
    __table_args__ = (
        CheckConstraint(
            "current_step IN ('PLANNING', 'TASK_ASSIGNMENT', 'TASK_EXECUTION', 'REVIEW', 'DONE')",
            name='ck_orchestration_memory_current_step'
        ),
        CheckConstraint(
            "length(content) > 0",
            name='ck_orchestration_memory_content_not_empty'
        ),
        CheckConstraint(
            "length(agent) > 0",
            name='ck_orchestration_memory_agent_not_empty'
        ),
        Index('idx_orchestration_memory_state_id', 'state_id'),
        Index('idx_orchestration_memory_agent', 'agent'),
        Index('idx_orchestration_memory_task_id', 'task_id'),
        Index('idx_orchestration_memory_current_step', 'current_step'),
        Index('idx_orchestration_memory_created_at', 'created_at'),
        Index('idx_orchestration_memory_state_agent', 'state_id', 'agent'),
        Index('idx_orchestration_memory_agent_step', 'agent', 'current_step'),
    )

    # Relationships
    state = relationship("OrchestrationState", back_populates="memory_units")

    def __repr__(self) -> str:
        return f"<OrchestrationMemory(id='{self.id}', agent='{self.agent}', step='{self.current_step}')>"

    def to_dict(self) -> Dict[str, Any]:
        """Convert memory unit to dictionary representation."""
        return {
            "id": self.id,
            "state_id": self.state_id,
            "content": self.content,
            "agent": self.agent,
            "task_id": self.task_id,
            "current_step": self.current_step,
            "metadata": self.memory_metadata or {},  # Map back to metadata in dict
            "tags": self.tags or [],
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


# Utility functions for working with models

def create_document(
    title: str,
    source: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> Document:
    """
    Create a new document instance.

    Args:
        title: Document title
        source: Document source (file path, URL, etc.)
        metadata: Additional metadata

    Returns:
        Document instance
    """
    return Document(
        title=title,
        source=source,
        doc_metadata=metadata or {}
    )


def create_document_chunk(
    document_id: str,
    content: str,
    metadata: Optional[Dict[str, Any]] = None
) -> DocumentChunk:
    """
    Create a new document chunk instance.

    Args:
        document_id: ID of the parent document
        content: Chunk content
        metadata: Additional metadata

    Returns:
        DocumentChunk instance
    """
    return DocumentChunk(
        document_id=document_id,
        content=content,
        chunk_metadata=metadata or {}
    )


def create_orchestration_state(
    user_input: str,
    context: Optional[Dict[str, Any]] = None,
    coordinator_plan_id: Optional[str] = None,
    current_step: str = "PLANNING"
) -> OrchestrationState:
    """
    Create a new orchestration state instance.

    Args:
        user_input: The user's input query
        context: Additional context
        coordinator_plan_id: Optional coordinator plan ID
        current_step: Current orchestration step

    Returns:
        OrchestrationState instance
    """
    return OrchestrationState(
        user_input=user_input,
        context=context or {},
        coordinator_plan_id=coordinator_plan_id,
        current_step=current_step
    )


def create_orchestration_task(
    state_id: str,
    description: str,
    task_type: str,
    status: str = "PENDING",
    assigned_agent: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> OrchestrationTask:
    """
    Create a new orchestration task instance.

    Args:
        state_id: ID of the parent state
        description: Task description
        task_type: Type of task
        status: Task status
        assigned_agent: Optional assigned agent
        metadata: Additional metadata

    Returns:
        OrchestrationTask instance
    """
    return OrchestrationTask(
        state_id=state_id,
        description=description,
        task_type=task_type,
        status=status,
        assigned_agent=assigned_agent,
        task_metadata=metadata or {}  # Updated to use task_metadata
    )


def create_orchestration_memory(
    state_id: str,
    content: str,
    agent: str,
    current_step: str,
    task_id: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    tags: Optional[List[str]] = None
) -> OrchestrationMemory:
    """
    Create a new orchestration memory instance.

    Args:
        state_id: ID of the parent state
        content: Memory content
        agent: Agent that generated the memory
        current_step: Current orchestration step
        task_id: Optional related task ID
        metadata: Additional metadata
        tags: Optional tags

    Returns:
        OrchestrationMemory instance
    """
    return OrchestrationMemory(
        state_id=state_id,
        content=content,
        agent=agent,
        current_step=current_step,
        task_id=task_id,
        memory_metadata=metadata or {},  # Updated to use memory_metadata
        tags=tags or []
    )
