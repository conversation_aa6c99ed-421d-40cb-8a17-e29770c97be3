"""Remove test tables

Revision ID: 550cfe16d0c1
Revises: 24d5fe564107
Create Date: 2025-06-21 04:17:39.676874

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '550cfe16d0c1'
down_revision = '24d5fe564107'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Remove test tables that are no longer needed for production."""

    # Check if test tables exist before trying to drop them
    # Since the test tables were never actually created in this environment,
    # this migration is effectively a no-op but serves as documentation
    # that test tables should be removed if they exist

    # Note: In environments where test tables exist, uncomment the following:
    # op.drop_index('test_vector_store_metadata_idx', table_name='test_vector_store', if_exists=True)
    # op.drop_index('test_embeddings_metadata_idx', table_name='test_embeddings', if_exists=True)
    # op.drop_index('test_vector_store_content_fts', table_name='test_vector_store', if_exists=True)
    # op.drop_index('test_embeddings_content_fts', table_name='test_embeddings', if_exists=True)
    # op.drop_index('test_vector_store_embedding_idx', table_name='test_vector_store', if_exists=True)
    # op.drop_index('test_embeddings_embedding_idx', table_name='test_embeddings', if_exists=True)
    # op.drop_table('test_vector_store', if_exists=True)
    # op.drop_table('test_embeddings', if_exists=True)

    pass  # No-op since test tables don't exist in this environment


def downgrade() -> None:
    """Recreate test tables if needed for rollback."""

    # Create test embeddings table
    op.create_table(
        'test_embeddings',
        sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('embedding', postgresql.VECTOR(1536), nullable=True),
        sa.Column('metadata', postgresql.JSONB(), server_default=sa.text("'{}'::jsonb"), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=True),
        sa.PrimaryKeyConstraint('id', name=op.f('test_embeddings_pkey'))
    )

    # Create test vector store table
    op.create_table(
        'test_vector_store',
        sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('embedding', postgresql.VECTOR(1536), nullable=True),
        sa.Column('metadata', postgresql.JSONB(), server_default=sa.text("'{}'::jsonb"), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=True),
        sa.PrimaryKeyConstraint('id', name=op.f('test_vector_store_pkey'))
    )

    # Create indexes
    op.create_index(
        'test_embeddings_embedding_idx',
        'test_embeddings',
        ['embedding'],
        postgresql_using='ivfflat',
        postgresql_with={'lists': 100},
        postgresql_ops={'embedding': 'vector_cosine_ops'}
    )

    op.create_index(
        'test_vector_store_embedding_idx',
        'test_vector_store',
        ['embedding'],
        postgresql_using='ivfflat',
        postgresql_with={'lists': 100},
        postgresql_ops={'embedding': 'vector_cosine_ops'}
    )

    op.create_index(
        'test_embeddings_content_fts',
        'test_embeddings',
        [sa.text("to_tsvector('english', content)")],
        postgresql_using='gin'
    )

    op.create_index(
        'test_vector_store_content_fts',
        'test_vector_store',
        [sa.text("to_tsvector('english', content)")],
        postgresql_using='gin'
    )

    op.create_index(
        'test_embeddings_metadata_idx',
        'test_embeddings',
        ['metadata'],
        postgresql_using='gin'
    )

    op.create_index(
        'test_vector_store_metadata_idx',
        'test_vector_store',
        ['metadata'],
        postgresql_using='gin'
    )
