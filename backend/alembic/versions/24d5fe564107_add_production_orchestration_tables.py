"""Add production orchestration tables

Revision ID: 24d5fe564107
Revises: c4d505593ad7
Create Date: 2025-06-21 04:04:55.042508

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '24d5fe564107'
down_revision = 'c4d505593ad7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create production orchestration tables with proper constraints and indexes."""

    # Create orchestration_states table
    op.create_table(
        'orchestration_states',
        sa.Column('id', sa.String(64), primary_key=True, nullable=False),
        sa.Column('user_input', sa.Text(), nullable=False),
        sa.Column('context', postgresql.JSONB(), nullable=True),
        sa.Column('coordinator_plan_id', sa.String(64), nullable=True),
        sa.Column('current_step', sa.String(32), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.func.now(), nullable=True),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_orchestration_states')),
        # Add check constraint for valid orchestration steps
        sa.CheckConstraint(
            "current_step IN ('PLANNING', 'TASK_ASSIGNMENT', 'TASK_EXECUTION', 'REVIEW', 'DONE')",
            name='ck_orchestration_states_current_step'
        )
    )

    # Create orchestration_tasks table
    op.create_table(
        'orchestration_tasks',
        sa.Column('id', sa.String(64), primary_key=True, nullable=False),
        sa.Column('state_id', sa.String(64), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('task_type', sa.String(32), nullable=False),
        sa.Column('status', sa.String(32), nullable=False),
        sa.Column('assigned_agent', sa.String(64), nullable=True),
        sa.Column('output', postgresql.JSONB(), nullable=True),
        sa.Column('task_metadata', postgresql.JSONB(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.func.now(), nullable=True),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_orchestration_tasks')),
        sa.ForeignKeyConstraint(['state_id'], ['orchestration_states.id'], ondelete='CASCADE', name=op.f('fk_orchestration_tasks_state_id')),
        # Add check constraints for valid enum values
        sa.CheckConstraint(
            "task_type IN ('QUERY_DECOMPOSITION', 'REASONING', 'COMMUNICATION', 'TOOLS', 'ACTION', 'EVALUATION')",
            name='ck_orchestration_tasks_task_type'
        ),
        sa.CheckConstraint(
            "status IN ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED')",
            name='ck_orchestration_tasks_status'
        )
    )

    # Create task_dependencies table (many-to-many for task dependencies)
    op.create_table(
        'task_dependencies',
        sa.Column('task_id', sa.String(64), nullable=False),
        sa.Column('dependency_id', sa.String(64), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.PrimaryKeyConstraint('task_id', 'dependency_id', name=op.f('pk_task_dependencies')),
        sa.ForeignKeyConstraint(['task_id'], ['orchestration_tasks.id'], ondelete='CASCADE', name=op.f('fk_task_dependencies_task_id')),
        sa.ForeignKeyConstraint(['dependency_id'], ['orchestration_tasks.id'], ondelete='CASCADE', name=op.f('fk_task_dependencies_dependency_id')),
        # Prevent self-referencing dependencies
        sa.CheckConstraint('task_id != dependency_id', name='ck_task_dependencies_no_self_reference')
    )

    # Create orchestration_memory table
    op.create_table(
        'orchestration_memory',
        sa.Column('id', sa.String(64), primary_key=True, nullable=False),
        sa.Column('state_id', sa.String(64), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('agent', sa.String(64), nullable=False),
        sa.Column('task_id', sa.String(64), nullable=True),
        sa.Column('current_step', sa.String(32), nullable=False),
        sa.Column('memory_metadata', postgresql.JSONB(), nullable=True),
        sa.Column('tags', postgresql.JSONB(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_orchestration_memory')),
        sa.ForeignKeyConstraint(['state_id'], ['orchestration_states.id'], ondelete='CASCADE', name=op.f('fk_orchestration_memory_state_id')),
        # Add check constraint for valid orchestration steps
        sa.CheckConstraint(
            "current_step IN ('PLANNING', 'TASK_ASSIGNMENT', 'TASK_EXECUTION', 'REVIEW', 'DONE')",
            name='ck_orchestration_memory_current_step'
        )
    )

    # Add embedding column to orchestration_memory using raw SQL (pgvector VECTOR type)
    op.execute("ALTER TABLE orchestration_memory ADD COLUMN embedding vector(768)")

    # Create performance indexes

    # Indexes for orchestration_states
    op.create_index(
        'idx_orchestration_states_current_step',
        'orchestration_states',
        ['current_step']
    )
    op.create_index(
        'idx_orchestration_states_created_at',
        'orchestration_states',
        ['created_at']
    )
    op.create_index(
        'idx_orchestration_states_context',
        'orchestration_states',
        ['context'],
        postgresql_using='gin'
    )

    # Indexes for orchestration_tasks
    op.create_index(
        'idx_orchestration_tasks_state_id',
        'orchestration_tasks',
        ['state_id']
    )
    op.create_index(
        'idx_orchestration_tasks_status',
        'orchestration_tasks',
        ['status']
    )
    op.create_index(
        'idx_orchestration_tasks_assigned_agent',
        'orchestration_tasks',
        ['assigned_agent']
    )
    op.create_index(
        'idx_orchestration_tasks_task_type',
        'orchestration_tasks',
        ['task_type']
    )
    op.create_index(
        'idx_orchestration_tasks_created_at',
        'orchestration_tasks',
        ['created_at']
    )
    op.create_index(
        'idx_orchestration_tasks_metadata',
        'orchestration_tasks',
        ['task_metadata'],
        postgresql_using='gin'
    )

    # Indexes for task_dependencies
    op.create_index(
        'idx_task_dependencies_dependency_id',
        'task_dependencies',
        ['dependency_id']
    )

    # Indexes for orchestration_memory
    op.create_index(
        'idx_orchestration_memory_state_id',
        'orchestration_memory',
        ['state_id']
    )
    op.create_index(
        'idx_orchestration_memory_agent',
        'orchestration_memory',
        ['agent']
    )
    op.create_index(
        'idx_orchestration_memory_task_id',
        'orchestration_memory',
        ['task_id']
    )
    op.create_index(
        'idx_orchestration_memory_current_step',
        'orchestration_memory',
        ['current_step']
    )
    op.create_index(
        'idx_orchestration_memory_created_at',
        'orchestration_memory',
        ['created_at']
    )
    op.create_index(
        'idx_orchestration_memory_tags',
        'orchestration_memory',
        ['tags'],
        postgresql_using='gin'
    )
    op.create_index(
        'idx_orchestration_memory_metadata',
        'orchestration_memory',
        ['memory_metadata'],
        postgresql_using='gin'
    )

    # Vector similarity index for semantic search
    op.create_index(
        'idx_orchestration_memory_embedding',
        'orchestration_memory',
        ['embedding'],
        postgresql_using='ivfflat',
        postgresql_with={'lists': 100},
        postgresql_ops={'embedding': 'vector_cosine_ops'}
    )

    # Full-text search index for memory content
    op.create_index(
        'idx_orchestration_memory_content_fts',
        'orchestration_memory',
        [sa.text("to_tsvector('english', content)")],
        postgresql_using='gin'
    )

    # Composite indexes for common query patterns
    op.create_index(
        'idx_orchestration_memory_state_agent',
        'orchestration_memory',
        ['state_id', 'agent']
    )
    op.create_index(
        'idx_orchestration_memory_agent_step',
        'orchestration_memory',
        ['agent', 'current_step']
    )
    op.create_index(
        'idx_orchestration_tasks_state_status',
        'orchestration_tasks',
        ['state_id', 'status']
    )


def downgrade() -> None:
    """Drop production orchestration tables in reverse order."""

    # Drop indexes first (in reverse order of creation)

    # Composite indexes
    op.drop_index('idx_orchestration_tasks_state_status')
    op.drop_index('idx_orchestration_memory_agent_step')
    op.drop_index('idx_orchestration_memory_state_agent')

    # Memory indexes
    op.drop_index('idx_orchestration_memory_content_fts')
    op.drop_index('idx_orchestration_memory_embedding')
    op.drop_index('idx_orchestration_memory_metadata')
    op.drop_index('idx_orchestration_memory_tags')
    op.drop_index('idx_orchestration_memory_created_at')
    op.drop_index('idx_orchestration_memory_current_step')
    op.drop_index('idx_orchestration_memory_task_id')
    op.drop_index('idx_orchestration_memory_agent')
    op.drop_index('idx_orchestration_memory_state_id')

    # Task dependency indexes
    op.drop_index('idx_task_dependencies_dependency_id')

    # Task indexes
    op.drop_index('idx_orchestration_tasks_metadata')
    op.drop_index('idx_orchestration_tasks_created_at')
    op.drop_index('idx_orchestration_tasks_task_type')
    op.drop_index('idx_orchestration_tasks_assigned_agent')
    op.drop_index('idx_orchestration_tasks_status')
    op.drop_index('idx_orchestration_tasks_state_id')

    # State indexes
    op.drop_index('idx_orchestration_states_context')
    op.drop_index('idx_orchestration_states_created_at')
    op.drop_index('idx_orchestration_states_current_step')

    # Drop tables in reverse order to respect foreign key constraints
    op.drop_table('orchestration_memory')
    op.drop_table('task_dependencies')
    op.drop_table('orchestration_tasks')
    op.drop_table('orchestration_states')
