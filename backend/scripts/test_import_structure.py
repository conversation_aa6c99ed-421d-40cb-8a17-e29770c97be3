#!/usr/bin/env python3
"""
Test Import Structure

This script tests the import structure to identify any remaining circular import issues.
"""

import os
import sys
import logging

# Add backend to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_import_order():
    """Test imports in the correct order to identify circular dependencies."""
    
    print("Testing import structure...")
    
    # Test 1: Base LLM utils (should work independently)
    print("\n1. Testing base LLM utils...")
    try:
        from llm.utils.model_registry import get_model_registry
        print("✓ Model registry imported successfully")
    except Exception as e:
        print(f"✗ Model registry import failed: {e}")
        return False
    
    try:
        from llm.utils.config import LLMConfig
        print("✓ LLM config imported successfully")
    except Exception as e:
        print(f"✗ LLM config import failed: {e}")
        return False
    
    # Test 2: LLM factory (uses lazy imports)
    print("\n2. Testing LLM factory...")
    try:
        from llm.utils.factory import get_llm_adapter
        print("✓ LLM factory imported successfully")
    except Exception as e:
        print(f"✗ LLM factory import failed: {e}")
        return False
    
    # Test 3: Individual adapters
    print("\n3. Testing individual adapters...")
    try:
        from llm.mock import MockAdapter
        print("✓ Mock adapter imported successfully")
    except Exception as e:
        print(f"✗ Mock adapter import failed: {e}")
        return False
    
    try:
        from llm.openai import OpenAIAdapter, OPENAI_AVAILABLE
        print(f"✓ OpenAI adapter imported successfully (available: {OPENAI_AVAILABLE})")
    except Exception as e:
        print(f"✗ OpenAI adapter import failed: {e}")
        return False
    
    # Test 4: LLM package init
    print("\n4. Testing LLM package init...")
    try:
        import llm
        print("✓ LLM package imported successfully")
    except Exception as e:
        print(f"✗ LLM package import failed: {e}")
        return False
    
    # Test 5: RAG config (with consolidated imports)
    print("\n5. Testing RAG config...")
    try:
        from rag.utils.config import RAGSettings
        print("✓ RAG config classes imported successfully")
    except Exception as e:
        print(f"✗ RAG config classes import failed: {e}")
        return False

    try:
        from rag.utils.config import rag_settings
        print("✓ RAG settings instance imported successfully")
    except Exception as e:
        print(f"✗ RAG settings instance import failed: {e}")
        return False
    
    # Test 6: RAG LLM adapter
    print("\n6. Testing RAG LLM adapter...")
    try:
        from rag.llm import get_rag_llm_adapter
        print("✓ RAG LLM adapter imported successfully")
    except Exception as e:
        print(f"✗ RAG LLM adapter import failed: {e}")
        return False
    
    # Test 7: Full RAG package
    print("\n7. Testing full RAG package...")
    try:
        import rag
        print("✓ RAG package imported successfully")
    except Exception as e:
        print(f"✗ RAG package import failed: {e}")
        return False
    
    return True

def test_functionality():
    """Test that the imported modules actually work."""
    
    print("\n" + "="*50)
    print("Testing functionality...")
    
    try:
        # Test model registry
        from llm.utils.model_registry import get_provider_for_model
        provider = get_provider_for_model("gpt-4o")
        print(f"✓ Model registry working: gpt-4o -> {provider}")
        
        # Test LLM config
        from llm.utils.config import LLMConfig
        config = LLMConfig(model="claude-3-5-sonnet-20241022")
        print(f"✓ LLM config working: {config.model}")
        
        # Test factory
        from llm.utils.factory import get_llm_adapter
        adapter = get_llm_adapter(model="gpt-4o-mini", fallback=True)
        print(f"✓ Factory working: created {type(adapter).__name__}")
        
        # Test RAG adapter
        from rag.llm import get_rag_llm_adapter
        rag_adapter = get_rag_llm_adapter()
        print(f"✓ RAG adapter working: created {type(rag_adapter).__name__}")
        
        return True
        
    except Exception as e:
        print(f"✗ Functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all import tests."""
    print("Import Structure Test Suite")
    print("=" * 50)
    
    # Test import order
    if not test_import_order():
        print("\n✗ Import structure test FAILED")
        sys.exit(1)
    
    # Test functionality
    if not test_functionality():
        print("\n✗ Functionality test FAILED")
        sys.exit(1)
    
    print("\n" + "="*50)
    print("✅ ALL IMPORT TESTS PASSED")
    print("✅ Import structure is working correctly")
    print("✅ No circular import issues detected")
    print("✅ All modules are functional")

if __name__ == "__main__":
    main()
