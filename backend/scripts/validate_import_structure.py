#!/usr/bin/env python3
"""
Import Structure Validation

This script validates that all LLM-related modules can be imported correctly
from different contexts (scripts, tests, main application) and that there are
no circular import issues.
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

# Add backend to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_direct_imports():
    """Test direct imports of core LLM modules."""
    print("🔍 Testing Direct Imports...")
    
    import_tests = [
        # Core LLM utils
        ("llm.utils.model_registry", "get_model_registry"),
        ("llm.utils.config", "LLMConfig"),
        ("llm.utils.factory", "get_llm_adapter"),
        
        # Individual adapters
        ("llm.mock", "MockAdapter"),
        ("llm.openai", "OpenAIAdapter"),
        ("llm.anthropic", "AnthropicAdapter"),
        ("llm.gemini", "GeminiAdapter"),
        
        # LLM package
        ("llm", "get_llm_adapter"),
    ]
    
    success = True
    for module, symbol in import_tests:
        try:
            exec(f"from {module} import {symbol}")
            print(f"   ✅ {module}.{symbol}")
        except Exception as e:
            print(f"   ❌ {module}.{symbol}: {e}")
            success = False
    
    return success

def test_script_context():
    """Test imports from script context (like our current context)."""
    print("\n📜 Testing Script Context...")
    
    backend_path = os.path.dirname(os.path.dirname(__file__))  # Go up two levels from scripts/
    script_content = f'''
import sys
import os

# Add the backend directory to Python path
backend_path = r"{backend_path}"
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

try:
    from llm.utils.model_registry import get_provider_for_model
    from llm.utils.config import LLMConfig
    from llm.utils.factory import get_llm_adapter

    # Test functionality
    provider = get_provider_for_model("gpt-4o")
    config = LLMConfig(model="claude-3-5-sonnet-20241022")
    adapter = get_llm_adapter(model="gpt-4o-mini", fallback=True)

    print(f"SUCCESS: provider={{provider}}, model={{config.model}}, adapter={{type(adapter).__name__}}")
except Exception as e:
    import traceback
    print(f"ERROR: {{e}}")
    traceback.print_exc()
    sys.exit(1)
'''
    
    try:
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(script_content)
            script_path = f.name
        
        # Run the script
        result = subprocess.run(
            [sys.executable, script_path],
            cwd=os.path.dirname(__file__),
            capture_output=True,
            text=True,
            timeout=30
        )
        
        os.unlink(script_path)
        
        if result.returncode == 0:
            print(f"   ✅ Script context: {result.stdout.strip()}")
            return True
        else:
            error_msg = result.stderr.strip() or result.stdout.strip() or "Unknown error"
            print(f"   ❌ Script context failed: {error_msg}")
            return False
            
    except Exception as e:
        print(f"   ❌ Script context error: {e}")
        return False

def test_module_context():
    """Test imports from module context (when imported as part of a package)."""
    print("\n📦 Testing Module Context...")
    
    try:
        # Test importing the LLM package as a whole
        import llm
        print(f"   ✅ LLM package imported: {len(llm.__all__)} exports")
        
        # Test accessing key functionality
        from llm.utils import get_llm_adapter, LLMConfig
        config = LLMConfig(component_type="rag")
        adapter = get_llm_adapter(config=config, fallback=True)
        print(f"   ✅ Module functionality: {type(adapter).__name__}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Module context failed: {e}")
        return False

def test_circular_imports():
    """Test for circular import issues."""
    print("\n🔄 Testing Circular Import Detection...")
    
    # Test importing in different orders to detect circular dependencies
    import_orders = [
        # Order 1: Factory first
        ["llm.utils.factory", "llm.openai", "llm.anthropic", "llm.gemini"],
        
        # Order 2: Adapters first
        ["llm.openai", "llm.anthropic", "llm.gemini", "llm.utils.factory"],
        
        # Order 3: Mixed order
        ["llm.utils.config", "llm.openai", "llm.utils.factory", "llm.anthropic"],
    ]
    
    success = True
    for i, order in enumerate(import_orders, 1):
        try:
            # Clear any previously imported modules
            modules_to_clear = [mod for mod in sys.modules.keys() if mod.startswith('llm.')]
            for mod in modules_to_clear:
                if mod in sys.modules:
                    del sys.modules[mod]
            
            # Import in the specified order
            for module in order:
                __import__(module)
            
            print(f"   ✅ Import order {i}: {' → '.join(order)}")
            
        except Exception as e:
            print(f"   ❌ Import order {i} failed: {e}")
            success = False
    
    return success

def test_lazy_initialization():
    """Test that lazy initialization works correctly."""
    print("\n⏳ Testing Lazy Initialization...")
    
    try:
        # Import factory without triggering initialization
        from llm.utils.factory import _llm_factory
        
        # Check that adapters are not initialized yet
        if not _llm_factory._initialized:
            print("   ✅ Factory not initialized on import")
        else:
            print("   ⚠️  Factory was already initialized")
        
        # Trigger initialization
        adapters = _llm_factory.get_supported_providers()
        
        # Check that initialization happened
        if _llm_factory._initialized:
            print(f"   ✅ Factory initialized on demand: {len(adapters)} providers")
        else:
            print("   ❌ Factory failed to initialize")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Lazy initialization failed: {e}")
        return False

def test_fallback_mechanisms():
    """Test that fallback mechanisms work correctly."""
    print("\n🛡️  Testing Fallback Mechanisms...")
    
    try:
        from llm.utils.factory import get_llm_adapter

        # Test fallback with invalid model
        adapter = get_llm_adapter(model="invalid-model", fallback=True)
        print(f"   ✅ Invalid model fallback: {type(adapter).__name__}")

        # Test fallback with invalid provider
        adapter = get_llm_adapter(provider="invalid-provider", fallback=True)
        print(f"   ✅ Invalid provider fallback: {type(adapter).__name__}")

        # Test no fallback (should raise error)
        try:
            adapter = get_llm_adapter(model="invalid-model", fallback=False)
            print("   ❌ No fallback should have raised error")
            return False
        except ValueError:
            print("   ✅ No fallback correctly raised error")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Fallback mechanisms failed: {e}")
        return False

def main():
    """Run all import structure validation tests."""
    print("🔧 Import Structure Validation")
    print("=" * 50)
    
    tests = [
        ("Direct Imports", test_direct_imports),
        ("Script Context", test_script_context),
        ("Module Context", test_module_context),
        ("Circular Imports", test_circular_imports),
        ("Lazy Initialization", test_lazy_initialization),
        ("Fallback Mechanisms", test_fallback_mechanisms),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        all_passed &= passed
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL IMPORT STRUCTURE TESTS PASSED!")
        print("✅ No circular import issues detected")
        print("✅ Lazy initialization working correctly")
        print("✅ Fallback mechanisms working correctly")
        print("✅ Imports work from all contexts")
        print("✅ System is robust and ready for production")
    else:
        print("❌ SOME IMPORT STRUCTURE TESTS FAILED")
        print("Please review the output above for details")
        sys.exit(1)

if __name__ == "__main__":
    main()
