#!/usr/bin/env python3
"""
Migration hooks for data transformation and backfill operations.
Provides pre/post-migration hooks for safe data migration from existing systems.
"""
import asyncio
import logging
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MigrationHookError(Exception):
    """Exception for migration hook failures."""
    pass

class DataTransformationHooks:
    """Handles data transformation and migration hooks."""
    
    def __init__(self):
        self.pre_hooks: List[Callable] = []
        self.post_hooks: List[Callable] = []
        self.transformation_rules: Dict[str, Callable] = {}
        
    def register_pre_hook(self, hook_func: Callable) -> None:
        """Register a pre-migration hook."""
        self.pre_hooks.append(hook_func)
        logger.info(f"Registered pre-migration hook: {hook_func.__name__}")
    
    def register_post_hook(self, hook_func: Callable) -> None:
        """Register a post-migration hook."""
        self.post_hooks.append(hook_func)
        logger.info(f"Registered post-migration hook: {hook_func.__name__}")
    
    def register_transformation_rule(self, rule_name: str, transform_func: Callable) -> None:
        """Register a data transformation rule."""
        self.transformation_rules[rule_name] = transform_func
        logger.info(f"Registered transformation rule: {rule_name}")
    
    async def execute_pre_hooks(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute all pre-migration hooks."""
        logger.info("Executing pre-migration hooks...")
        
        for hook in self.pre_hooks:
            try:
                logger.info(f"Running pre-hook: {hook.__name__}")
                context = await hook(context) if asyncio.iscoroutinefunction(hook) else hook(context)
            except Exception as e:
                logger.error(f"Pre-hook {hook.__name__} failed: {e}")
                raise MigrationHookError(f"Pre-migration hook failed: {e}")
        
        logger.info("All pre-migration hooks completed successfully")
        return context
    
    async def execute_post_hooks(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute all post-migration hooks."""
        logger.info("Executing post-migration hooks...")
        
        for hook in self.post_hooks:
            try:
                logger.info(f"Running post-hook: {hook.__name__}")
                context = await hook(context) if asyncio.iscoroutinefunction(hook) else hook(context)
            except Exception as e:
                logger.error(f"Post-hook {hook.__name__} failed: {e}")
                raise MigrationHookError(f"Post-migration hook failed: {e}")
        
        logger.info("All post-migration hooks completed successfully")
        return context
    
    def transform_data(self, data: Dict[str, Any], rule_name: str) -> Dict[str, Any]:
        """Apply a transformation rule to data."""
        if rule_name not in self.transformation_rules:
            raise MigrationHookError(f"Transformation rule '{rule_name}' not found")
        
        transform_func = self.transformation_rules[rule_name]
        try:
            return transform_func(data)
        except Exception as e:
            logger.error(f"Data transformation failed for rule '{rule_name}': {e}")
            raise MigrationHookError(f"Data transformation failed: {e}")

# Global hooks instance
migration_hooks = DataTransformationHooks()

# Pre-migration hooks
async def backup_existing_data(context: Dict[str, Any]) -> Dict[str, Any]:
    """Backup existing data before migration."""
    logger.info("Creating backup of existing data...")
    
    try:
        from backend.app.core.db.database import get_async_db_session
        from sqlalchemy import text
        
        backup_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "tables": {}
        }
        
        # Tables to backup
        tables_to_backup = [
            "orchestration_states",
            "orchestration_tasks", 
            "orchestration_memory",
            "task_dependencies"
        ]
        
        async with get_async_db_session() as db:
            for table in tables_to_backup:
                try:
                    # Check if table exists
                    result = await db.execute(text(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_name = '{table}'
                        )
                    """))
                    
                    if result.scalar():
                        # Get row count
                        count_result = await db.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        row_count = count_result.scalar()
                        backup_data["tables"][table] = {"row_count": row_count}
                        logger.info(f"Table {table}: {row_count} rows")
                    else:
                        backup_data["tables"][table] = {"row_count": 0, "note": "table_not_exists"}
                        logger.info(f"Table {table}: does not exist")
                        
                except Exception as e:
                    logger.warning(f"Could not backup table {table}: {e}")
                    backup_data["tables"][table] = {"error": str(e)}
        
        # Save backup metadata
        backup_file = Path("migration_backup.json")
        with open(backup_file, "w") as f:
            json.dump(backup_data, f, indent=2)
        
        context["backup_file"] = str(backup_file)
        context["backup_data"] = backup_data
        logger.info(f"Backup metadata saved to {backup_file}")
        
    except Exception as e:
        logger.error(f"Backup failed: {e}")
        raise MigrationHookError(f"Backup operation failed: {e}")
    
    return context

def validate_migration_environment(context: Dict[str, Any]) -> Dict[str, Any]:
    """Validate the migration environment."""
    logger.info("Validating migration environment...")
    
    # Check required environment variables
    required_env_vars = ["DATABASE_URL"]
    missing_vars = []
    
    import os
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        raise MigrationHookError(f"Missing required environment variables: {missing_vars}")
    
    # Check database connectivity
    try:
        from backend.app.core.db.database import check_database_integrity
        health = check_database_integrity()
        
        if health["overall_status"] == "unhealthy":
            raise MigrationHookError(f"Database is unhealthy: {health['errors']}")
        
        context["database_health"] = health
        logger.info(f"Database health: {health['overall_status']}")
        
    except Exception as e:
        raise MigrationHookError(f"Database validation failed: {e}")
    
    return context

# Post-migration hooks
async def verify_migration_integrity(context: Dict[str, Any]) -> Dict[str, Any]:
    """Verify migration integrity after completion."""
    logger.info("Verifying migration integrity...")
    
    try:
        from backend.app.core.db.database import get_async_db_session
        from sqlalchemy import text
        
        verification_results = {
            "tables_verified": {},
            "indexes_verified": {},
            "constraints_verified": {}
        }
        
        async with get_async_db_session() as db:
            # Verify table existence and structure
            required_tables = [
                "orchestration_states",
                "orchestration_tasks",
                "orchestration_memory", 
                "task_dependencies"
            ]
            
            for table in required_tables:
                result = await db.execute(text(f"""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns
                    WHERE table_name = '{table}'
                    ORDER BY ordinal_position
                """))
                
                columns = result.fetchall()
                verification_results["tables_verified"][table] = {
                    "exists": len(columns) > 0,
                    "column_count": len(columns),
                    "columns": [{"name": col[0], "type": col[1], "nullable": col[2]} for col in columns]
                }
                
                logger.info(f"Table {table}: {len(columns)} columns verified")
            
            # Verify critical indexes
            critical_indexes = [
                "idx_orchestration_memory_state_id",
                "idx_orchestration_memory_embedding",
                "idx_orchestration_states_current_step"
            ]
            
            for index in critical_indexes:
                result = await db.execute(text(f"""
                    SELECT indexname FROM pg_indexes 
                    WHERE indexname = '{index}'
                """))
                
                exists = result.fetchone() is not None
                verification_results["indexes_verified"][index] = exists
                logger.info(f"Index {index}: {'exists' if exists else 'missing'}")
        
        context["verification_results"] = verification_results
        
        # Check for any critical failures
        missing_tables = [t for t, info in verification_results["tables_verified"].items() if not info["exists"]]
        missing_indexes = [i for i, exists in verification_results["indexes_verified"].items() if not exists]
        
        if missing_tables:
            raise MigrationHookError(f"Critical tables missing after migration: {missing_tables}")
        
        if missing_indexes:
            logger.warning(f"Some indexes are missing: {missing_indexes}")
        
        logger.info("Migration integrity verification completed successfully")
        
    except Exception as e:
        logger.error(f"Migration verification failed: {e}")
        raise MigrationHookError(f"Migration verification failed: {e}")
    
    return context

async def initialize_default_data(context: Dict[str, Any]) -> Dict[str, Any]:
    """Initialize default data after migration."""
    logger.info("Initializing default data...")
    
    try:
        # Test memory system functionality
        from backend.orchestration.memory import GlobalMemory
        from backend.orchestration.state import OrchestrationStep, LangGraphState
        from backend.orchestration.db import save_state
        
        # Create a test state
        test_state_id = f"migration-test-{uuid.uuid4().hex[:8]}"
        test_state = LangGraphState(
            id=test_state_id,
            user_input="Migration test state",
            context={"migration": True},
            coordinator_plan_id=None,
            tasks=[],
            current_task_id=None,
            current_step=OrchestrationStep.PLANNING
        )
        
        await save_state(test_state)
        
        # Test memory operations
        memory = GlobalMemory(auto_persist=True, session_state_id=test_state_id)
        await memory.initialize_session(test_state_id, load_existing=False)
        
        # Add a test memory
        memory_id = await memory.add_memory(
            content="Migration test memory - system is working correctly",
            agent="migration-test-agent",
            current_step=OrchestrationStep.PLANNING,
            metadata={"test": True, "migration_timestamp": datetime.now(timezone.utc).isoformat()},
            tags=["migration", "test", "system-check"]
        )
        
        # Verify memory retrieval
        memories = await memory.retrieve_memory(state_id=test_state_id, limit=1)
        
        if len(memories) == 1 and memories[0].memory_id == memory_id:
            logger.info("Memory system test: PASSED")
            context["memory_test"] = "passed"
        else:
            raise MigrationHookError("Memory system test failed")
        
        # Clean up test data
        await memory.delete_memory(memory_id, test_state_id)
        
        from backend.app.core.db.database import get_async_db_session
        from sqlalchemy import text
        
        async with get_async_db_session() as db:
            await db.execute(text("DELETE FROM orchestration_states WHERE id = :state_id"), {"state_id": test_state_id})
            await db.commit()
        
        logger.info("Default data initialization completed successfully")
        
    except Exception as e:
        logger.error(f"Default data initialization failed: {e}")
        raise MigrationHookError(f"Default data initialization failed: {e}")
    
    return context

# Data transformation rules
def transform_legacy_memory_format(data: Dict[str, Any]) -> Dict[str, Any]:
    """Transform legacy memory format to new format."""
    transformed = data.copy()
    
    # Example transformations
    if "old_content_field" in data:
        transformed["content"] = data["old_content_field"]
        del transformed["old_content_field"]
    
    if "old_agent_field" in data:
        transformed["agent"] = data["old_agent_field"]
        del transformed["old_agent_field"]
    
    # Ensure required fields exist
    if "current_step" not in transformed:
        transformed["current_step"] = "PLANNING"  # Default step
    
    if "metadata" not in transformed:
        transformed["metadata"] = {}
    
    if "tags" not in transformed:
        transformed["tags"] = []
    
    return transformed

def transform_legacy_state_format(data: Dict[str, Any]) -> Dict[str, Any]:
    """Transform legacy state format to new format."""
    transformed = data.copy()
    
    # Example transformations for state data
    if "old_context_field" in data:
        transformed["context"] = data["old_context_field"]
        del transformed["old_context_field"]
    
    # Ensure required fields
    if "current_step" not in transformed:
        transformed["current_step"] = "PLANNING"
    
    return transformed

# Register hooks and transformation rules
migration_hooks.register_pre_hook(backup_existing_data)
migration_hooks.register_pre_hook(validate_migration_environment)
migration_hooks.register_post_hook(verify_migration_integrity)
migration_hooks.register_post_hook(initialize_default_data)

migration_hooks.register_transformation_rule("legacy_memory", transform_legacy_memory_format)
migration_hooks.register_transformation_rule("legacy_state", transform_legacy_state_format)

async def run_migration_with_hooks():
    """Run a complete migration with all hooks."""
    logger.info("Starting migration with hooks...")
    
    context = {
        "migration_start": datetime.now(timezone.utc).isoformat(),
        "migration_id": str(uuid.uuid4())
    }
    
    try:
        # Execute pre-migration hooks
        context = await migration_hooks.execute_pre_hooks(context)
        
        # Run actual migration (this would be your Alembic upgrade)
        logger.info("Running database migration...")
        import subprocess
        result = subprocess.run(["alembic", "upgrade", "head"], capture_output=True, text=True)
        
        if result.returncode != 0:
            raise MigrationHookError(f"Alembic migration failed: {result.stderr}")
        
        context["migration_output"] = result.stdout
        logger.info("Database migration completed successfully")
        
        # Execute post-migration hooks
        context = await migration_hooks.execute_post_hooks(context)
        
        context["migration_end"] = datetime.now(timezone.utc).isoformat()
        context["status"] = "success"
        
        logger.info("Migration with hooks completed successfully")
        return context
        
    except Exception as e:
        context["migration_end"] = datetime.now(timezone.utc).isoformat()
        context["status"] = "failed"
        context["error"] = str(e)
        
        logger.error(f"Migration with hooks failed: {e}")
        raise

async def backfill_data_from_source(source_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Backfill data from an existing source system.

    Args:
        source_config: Configuration for the source system

    Returns:
        Dictionary with backfill results
    """
    logger.info("Starting data backfill operation...")

    results = {
        "start_time": datetime.now(timezone.utc).isoformat(),
        "records_processed": 0,
        "records_migrated": 0,
        "errors": [],
        "warnings": []
    }

    try:
        from backend.orchestration.memory import GlobalMemory
        from backend.orchestration.state import OrchestrationStep

        # Initialize memory system for backfill
        memory = GlobalMemory(auto_persist=True)

        # Example: Backfill from JSON file
        if source_config.get("type") == "json_file":
            file_path = source_config.get("file_path")
            if not file_path or not Path(file_path).exists():
                raise MigrationHookError(f"Source file not found: {file_path}")

            with open(file_path, 'r') as f:
                source_data = json.load(f)

            for record in source_data.get("memories", []):
                try:
                    results["records_processed"] += 1

                    # Transform data using registered rules
                    if source_config.get("transform_rule"):
                        record = migration_hooks.transform_data(record, source_config["transform_rule"])

                    # Add memory to new system
                    await memory.add_memory(
                        content=record.get("content", ""),
                        agent=record.get("agent", "migrated-agent"),
                        current_step=OrchestrationStep(record.get("current_step", "PLANNING")),
                        task_id=record.get("task_id"),
                        metadata=record.get("metadata", {}),
                        tags=record.get("tags", []),
                        state_id=record.get("state_id", "migrated-session")
                    )

                    results["records_migrated"] += 1

                except Exception as e:
                    error_msg = f"Failed to migrate record {results['records_processed']}: {e}"
                    logger.error(error_msg)
                    results["errors"].append(error_msg)

        # Example: Backfill from database
        elif source_config.get("type") == "database":
            from backend.app.core.db.database import get_async_db_session
            from sqlalchemy import text

            async with get_async_db_session() as db:
                # Query old data
                query = source_config.get("query", "SELECT * FROM old_memory_table")
                result = await db.execute(text(query))

                for row in result.fetchall():
                    try:
                        results["records_processed"] += 1

                        # Convert row to dict
                        record = dict(row._mapping)

                        # Transform data
                        if source_config.get("transform_rule"):
                            record = migration_hooks.transform_data(record, source_config["transform_rule"])

                        # Add to new system
                        await memory.add_memory(
                            content=record.get("content", ""),
                            agent=record.get("agent", "migrated-agent"),
                            current_step=OrchestrationStep(record.get("current_step", "PLANNING")),
                            task_id=record.get("task_id"),
                            metadata=record.get("metadata", {}),
                            tags=record.get("tags", []),
                            state_id=record.get("state_id", "migrated-session")
                        )

                        results["records_migrated"] += 1

                    except Exception as e:
                        error_msg = f"Failed to migrate database record: {e}"
                        logger.error(error_msg)
                        results["errors"].append(error_msg)

        else:
            raise MigrationHookError(f"Unsupported source type: {source_config.get('type')}")

        results["end_time"] = datetime.now(timezone.utc).isoformat()
        results["status"] = "completed"

        logger.info(f"Backfill completed: {results['records_migrated']}/{results['records_processed']} records migrated")

        if results["errors"]:
            logger.warning(f"Backfill completed with {len(results['errors'])} errors")

        return results

    except Exception as e:
        results["end_time"] = datetime.now(timezone.utc).isoformat()
        results["status"] = "failed"
        results["error"] = str(e)

        logger.error(f"Backfill operation failed: {e}")
        raise MigrationHookError(f"Data backfill failed: {e}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "backfill":
        # Example backfill configuration
        config = {
            "type": "json_file",
            "file_path": "legacy_data.json",
            "transform_rule": "legacy_memory"
        }
        asyncio.run(backfill_data_from_source(config))
    else:
        # Run migration with hooks
        asyncio.run(run_migration_with_hooks())
