"""
RAGAgent

Responsibilities:
- Centralized coordinator for Retrieval-Augmented Generation (RAG) across the orchestration system
- Performs document retrieval via the KnowledgeBaseService (e.g., hybrid, dense, keyword search)
- Enriches LangGraph state with retrieved context for downstream agents
- Optionally applies query rewriting, reranking, or filtering using retriever submodules
- Can be invoked by the planner, coordinator, or departmental agents to support task execution
- Optionally supports document ingestion if delegated by external components
- Coordinates with shared memory (GlobalMemory) for traceability or learning
"""

from typing import List, Dict, Any, Optional

from ...app.core.types import SearchResult
from ..departmental.base import BaseAgent


class RAGAgent(BaseAgent):
    def __init__(self, 
                 llm_adapter,  # RAGLLMAdapter (`rag.llm.RAGLLMAdapter`)
                 memory,  # GlobalMemory (`orchestration.memory.GlobalMemory`)
                 knowledge_base):  # KnowledgeBaseService (`rag.knowledge_base.KnowledgeBaseService`)
        """RAGAgent inherits from the super-/(-parent)-class -> BaseAgent."""
        super().__init__(llm_adapter, memory, knowledge_base)

    
    async def search(self, 
                     query: str,
                     limit: int = 5,
                     filters: Optional[Dict[str, Any]] = None) -> List[SearchResult]:
        """Search the knowledge base for (5) relevant documents.
        
        Args:
            query: The query string prompted by the user
            limit: The maximum number of results to return
            filters: Optional metadata filters
            
        Returns:
            List of SearchResults (defined in `app.core.types.SearchResult`)
        """
        return await self.knowledge_base.search(query, limit, filters)