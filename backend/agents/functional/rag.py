"""
RAGAgent

Responsibilities:
- Centralized coordinator for Retrieval-Augmented Generation (RAG) across the orchestration system
- Performs document retrieval via the KnowledgeBaseService (e.g., hybrid, dense, keyword search)
- Enriches LangGraph state with retrieved context for downstream agents
- Optionally applies query rewriting, reranking, or filtering using retriever submodules
- Can be invoked by the planner, coordinator, or departmental agents to support task execution
- Optionally supports document ingestion if delegated by external components
- Coordinates with shared memory (GlobalMemory) for traceability or learning
"""

import json
import hashlib
import logging
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime
from rag.knowledge_base import SearchResult

logger = logging.getLogger(__name__)


class RAGAgent:
    """
    llm_adapter: Optional but useful if planning to include LLM-based reranking, rephrasing, summarization, or scoring
    memory: Enables RAG traceability, preference tracking, or learning loops
    knowledge_base:	Core retrieval layer abstraction (hybrid/vector/etc.) 
    context_manager: Controls formatting, token budget fitting, relevance filtering (Note: Must implement `optimize_context()` and `fit_to_context_window()` methods.)
    query_rewriter: Enables rewrites based on task or memory context, user history, etc.
    quality_control: Central hook for filtering irrelevant or low-quality RAG results
    raw_cache_ttl: Configurable freshness vs performance tuning
    formatted_cache_ttl: Decouples formatted cache lifetime from raw cache
    """
    def __init__(self,
                 llm_adapter,  # RAGLLMAdapter (`rag.llm.RAGLLMAdapter`)
                 memory,  # GlobalMemory (`orchestration.memory.GlobalMemory`)
                 knowledge_base,  # KnowledgeBaseService (`rag.knowledge_base.KnowledgeBaseService`) 
                 context_manager,  # ContextManager (`rag.retriever.ContextManager`)
                 query_rewriter,  # QueryRewriter (`rag.retriever.QueryRewriter`)
                 quality_control,  # QualityControl (`rag.quality.QualityControl`)
                 raw_cache_ttl=600,  # 10 minutes
                 formatted_cache_ttl=1800): # 30 minutes
        
        # Initialize internal attributes
        self.llm_adapter = llm_adapter
        self.memory = memory
        self.knowledge_base = knowledge_base
        self.context_manager = context_manager
        self.query_rewriter = query_rewriter     
        self.quality_control = quality_control
        self.raw_cache_ttl = raw_cache_ttl
        self.formatted_cache_ttl = formatted_cache_ttl
        self.raw_cache: Dict[str, Tuple[datetime, List[Dict[str, Any]]]] = {}
        self.formatted_cache: Dict[str, Tuple[datetime, str]] = {}

    @staticmethod
    def _build_raw_cache_key(query: str, 
                             limit: int, 
                             filters: Optional[dict]) -> str:
        """Builds a deterministic cache key for raw RAG results."""
        raw_sorted_filters = sorted(filters.items()) if filters else []

        # Build the cache key structure
        raw_cache_data_structure = {
            "query": query,
            "limit": limit,
            "filters": raw_sorted_filters
        }

        # Serialize deterministically
        raw_json_string = json.dumps(raw_cache_data_structure, sort_keys=True, separators=(",", ":"))

        # Hash to make the cache key compact and safe
        raw_hash_key = hashlib.md5(raw_json_string.encode("utf-8")).hexdigest()

        return f"raw:{raw_hash_key}"

    
    async def _get_raw_cache(self, cache_key: str) -> Optional[List[SearchResult]]:
        """Retrieves raw RAG results from cache if still valid."""
        if cache_key not in self.raw_cache:
            return None
        timestamp, cached_items = self.raw_cache[cache_key]
        if (datetime.now() - timestamp).total_seconds() > self.raw_cache_ttl:
            logger.debug(f"Raw cache expired for key: {cache_key}")
            return None  # expired
        logger.debug(f"Raw cache hit for key: {cache_key}")

        return [SearchResult(**item) for item in cached_items]

    
    async def _set_raw_cache(self, cache_key: str, results: List[SearchResult]) -> None:
        """Stores raw RAG results in cache with TTL."""

        raw_search_result = [rsr.model_dump() for rsr in results]
        self.raw_cache[cache_key] = (datetime.now(), raw_search_result)


    def _build_formatted_cache_key(query: str , 
                                   limit: int, 
                                   filters: Optional[Dict[str, Any]], 
                                   format_params: Dict[str, Any]) -> str:
        """Builds a deterministic cache key for formatted RAG context."""
        formatted_sorted_filters = sorted(filters.items()) if filters else []

        # Build the cache key structure
        formatted_cache_data_structure = {
            "query": query,
            "limit": limit,
            "filters": formatted_sorted_filters,
            "format_params": format_params
        }

        # Serialize deterministically
        formatted_json_string = json.dumps(formatted_cache_data_structure, sort_keys=True, separators=(",", ":"))

        # Hash to make the cache key compact and safe
        formatted_hash_key = hashlib.md5(formatted_json_string.encode("utf-8")).hexdigest()

        return f"formatted:{formatted_hash_key}"
        

    async def search(self, 
           query: str,
           limit: int = 5,
           filters: Optional[Dict[str, Any]] = None,
           task_context: Optional[Dict[str, Any]] = None,
           use_cache: bool = True
) -> List[SearchResult]:
        logger.debug(f"Original query: {query}")

        if self.query_rewriter is not None:
            query = await self.query_rewriter.rewrite(query, task_context)
            logger.debug(f"Rewritten query: {query}")

        raw_cache_key = self._build_raw_cache_key(query, limit, filters)
        
        if use_cache:
            cached_results = await self._get_raw_cache(raw_cache_key)
            if cached_results is not None:
                logger.debug(f"Returning cached results for key: {raw_cache_key}")
                return cached_results

        try:
            search_results = await self.knowledge_base.search(query, limit=limit, filters=filters)
        except Exception as e:
            logger.error(f"Error searching knowledge base: {str(e)}")
            search_results = []    

        search_results = search_results or []
        logger.debug(f"Retrieved {len(search_results)} raw search results")

        await self._set_raw_cache(raw_cache_key, search_results)
        return search_results

    """Format context takes raw SearchResult objects and returns a formatted context string,
    suitable for inclusion in a system prompt."""
    def format_context(self, query: str, 
                      raw_results: List[SearchResult], 
                      task_context: Optional[Dict[str, Any]],
    ):
        formatted_search_result = [fsr.model_dump() for fsr in raw_results]
        return formatted_search_result
    

    def add_rag_context(self, ):
        pass
