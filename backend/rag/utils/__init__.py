"""
RAG Utilities

This module provides utility functions and classes for the RAG system.
"""

# Import config directly since it's the main consolidated module
from .config import (
    RAGConfig,
    RAGSettings,
    rag_settings,
    initialize_embedding_model,
    initialize_knowledge_base_service
)

# Other imports available but not imported at module level to avoid circular imports
# from .logging import get_logger, setup_logging
# from .timeout_retry import (...)
# from .validation import (...)

__all__ = [
    # Config - main consolidated exports
    "RAGConfig",
    "RAGSettings",
    "rag_settings",
    "initialize_embedding_model",
    "initialize_knowledge_base_service",
]