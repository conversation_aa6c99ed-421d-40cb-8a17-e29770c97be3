"""
Timeout and Retry Utilities

This module provides standardized timeout and retry decorators and utilities
for the RAG system. All timeout and retry configurations should be managed
through this module.
"""

import asyncio
import functools
import logging
import random
from typing import Any, Callable, Dict, Optional, Type, TypeVar, Union

from ...app.core.metrics import metrics
from .config import rag_settings

logger = logging.getLogger(__name__)

T = TypeVar('T')

def create_timeout_decorator(
    operation_name: str,
    metric_name: Optional[str] = None,
    timeout_seconds: Optional[float] = None
) -> Callable:
    """
    Create a timeout decorator for an operation.
    
    Args:
        operation_name: Name of the operation for logging
        metric_name: Optional metric name for tracking
        timeout_seconds: Optional timeout in seconds (defaults to config)
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            # Get timeout from config if not specified
            timeout = timeout_seconds or getattr(
                rag_settings,
                f"timeout_{operation_name.lower()}",
                rag_settings.timeout_llm  # Default to LLM timeout
            )
            
            try:
                # Create task with timeout
                task = asyncio.create_task(func(*args, **kwargs))
                result = await asyncio.wait_for(task, timeout=timeout)
                
                # Record success if metric name provided
                if metric_name:
                    metrics.record_success(metric_name, 0.0)  # Duration not tracked in timeout decorator
                
                return result
                
            except asyncio.TimeoutError:
                error_msg = f"Operation {operation_name} timed out after {timeout} seconds"
                logger.error(error_msg)
                
                # Record failure if metric name provided
                if metric_name:
                    metrics.record_failure(metric_name)
                
                raise TimeoutError(error_msg)
                
            except Exception as e:
                # Record failure if metric name provided
                if metric_name:
                    metrics.record_failure(metric_name)
                raise
                
        return wrapper
    return decorator

def create_retry_decorator(
    operation_name: str,
    metric_name: Optional[str] = None,
    max_retries: Optional[int] = None,
    retry_delay: Optional[float] = None,
    retry_jitter: Optional[float] = None,
    retry_exceptions: Optional[Union[Type[Exception], tuple[Type[Exception], ...]]] = None
) -> Callable:
    """
    Create a retry decorator for an operation.
    
    Args:
        operation_name: Name of the operation for logging
        metric_name: Optional metric name for tracking
        max_retries: Optional maximum number of retries (defaults to config)
        retry_delay: Optional delay between retries in seconds (defaults to config)
        retry_jitter: Optional jitter factor for retry delay (defaults to config)
        retry_exceptions: Optional exception types to retry on (defaults to all)
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            # Get retry settings from config if not specified
            max_retries_ = max_retries or rag_settings.max_retries
            retry_delay_ = retry_delay or rag_settings.retry_delay
            retry_jitter_ = retry_jitter or rag_settings.retry_jitter
            
            last_exception = None
            
            for attempt in range(max_retries_ + 1):
                try:
                    result = await func(*args, **kwargs)
                    
                    # Record success if metric name provided
                    if metric_name:
                        metrics.record_success(metric_name, 0.0)  # Duration not tracked in retry decorator
                    
                    return result
                    
                except Exception as e:
                    last_exception = e
                    
                    # Check if we should retry this exception
                    if retry_exceptions and not isinstance(e, retry_exceptions):
                        raise
                    
                    # Check if we've exhausted retries
                    if attempt == max_retries_:
                        break
                    
                    # Calculate delay with jitter
                    delay = retry_delay_ * (1 + random.uniform(-retry_jitter_, retry_jitter_))
                    
                    logger.warning(
                        f"Operation {operation_name} failed (attempt {attempt + 1}/{max_retries_ + 1}): {str(e)}. "
                        f"Retrying in {delay:.2f} seconds..."
                    )
                    
                    await asyncio.sleep(delay)
            
            # Record failure if metric name provided
            if metric_name:
                metrics.record_failure(metric_name)
            
            raise last_exception
            
        return wrapper
    return decorator

def create_timeout_and_retry_decorator(
    operation_name: str,
    metric_name: Optional[str] = None,
    timeout_seconds: Optional[float] = None,
    max_retries: Optional[int] = None,
    retry_delay: Optional[float] = None,
    retry_jitter: Optional[float] = None,
    retry_exceptions: Optional[Union[Type[Exception], tuple[Type[Exception], ...]]] = None
) -> Callable:
    """
    Create a combined timeout and retry decorator for an operation.
    
    Args:
        operation_name: Name of the operation for logging
        metric_name: Optional metric name for tracking
        timeout_seconds: Optional timeout in seconds (defaults to config)
        max_retries: Optional maximum number of retries (defaults to config)
        retry_delay: Optional delay between retries in seconds (defaults to config)
        retry_jitter: Optional jitter factor for retry delay (defaults to config)
        retry_exceptions: Optional exception types to retry on (defaults to all)
        
    Returns:
        Decorator function
    """
    timeout_decorator = create_timeout_decorator(
        operation_name=operation_name,
        metric_name=metric_name,
        timeout_seconds=timeout_seconds
    )
    
    retry_decorator = create_retry_decorator(
        operation_name=operation_name,
        metric_name=metric_name,
        max_retries=max_retries,
        retry_delay=retry_delay,
        retry_jitter=retry_jitter,
        retry_exceptions=retry_exceptions
    )
    
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        return timeout_decorator(retry_decorator(func))
    return decorator

# Pre-configured decorators for common operations
embedding_timeout = create_timeout_decorator(
    operation_name="embedding",
    metric_name="embedding_operation",
    timeout_seconds=rag_settings.timeout_embedding
)

vector_search_timeout = create_timeout_decorator(
    operation_name="vector_search",
    metric_name="vector_search_operation",
    timeout_seconds=rag_settings.timeout_vector_search
)

keyword_search_timeout = create_timeout_decorator(
    operation_name="keyword_search",
    metric_name="keyword_search_operation",
    timeout_seconds=rag_settings.timeout_keyword_search
)

hybrid_search_timeout = create_timeout_decorator(
    operation_name="hybrid_search",
    metric_name="hybrid_search_operation",
    timeout_seconds=rag_settings.timeout_hybrid_search
)

knowledge_base_timeout = create_timeout_decorator(
    operation_name="knowledge_base",
    metric_name="knowledge_base_operation",
    timeout_seconds=rag_settings.timeout_llm
)

# Combined decorators for common operations
embedding_operation = create_timeout_and_retry_decorator(
    operation_name="embedding",
    metric_name="embedding_operation",
    timeout_seconds=rag_settings.timeout_embedding
)

vector_search_operation = create_timeout_and_retry_decorator(
    operation_name="vector_search",
    metric_name="vector_search_operation",
    timeout_seconds=rag_settings.timeout_vector_search
)

keyword_search_operation = create_timeout_and_retry_decorator(
    operation_name="keyword_search",
    metric_name="keyword_search_operation",
    timeout_seconds=rag_settings.timeout_keyword_search
)

hybrid_search_operation = create_timeout_and_retry_decorator(
    operation_name="hybrid_search",
    metric_name="hybrid_search_operation",
    timeout_seconds=rag_settings.timeout_hybrid_search
)

knowledge_base_operation = create_timeout_and_retry_decorator(
    operation_name="knowledge_base",
    metric_name="knowledge_base_operation",
    timeout_seconds=rag_settings.timeout_llm
) 