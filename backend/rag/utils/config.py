"""
RAG Configuration - Single Source of Truth

This module provides the unified configuration system for the RAG implementation.
Consolidates all RAG-related configuration into a single, comprehensive system.
"""

import logging
from typing import Optional
from pydantic import Field, BaseModel

try:
    from pydantic_settings import BaseSettings
except ImportError:
    # Fallback for older pydantic versions
    from pydantic import BaseSettings

# Import moved to function to avoid circular import
# from ..embeddings import get_embedding_model, EmbeddingModel

logger = logging.getLogger(__name__)

# Try to import LLMConfig, with consolidated fallback
LLMConfig = None
try:
    from ...llm.utils.config import LLMConfig
except ImportError:
    try:
        from backend.llm.utils.config import LLMConfig
    except ImportError:
        # Single fallback LLMConfig implementation
        class LLMConfig:
            """Fallback LLM configuration."""
            def __init__(self, **kwargs):
                self.model = kwargs.get('model', 'gpt-4o-mini')
                self.temperature = kwargs.get('temperature', 0.7)
                self.max_tokens = kwargs.get('max_tokens', 1000)
                self.timeout = kwargs.get('timeout', 30.0)
                self.max_retries = kwargs.get('max_retries', 3)
                self.component_type = kwargs.get('component_type', None)


class EmbeddingConfig(BaseModel):
    """Configuration for embedding models."""
    model_name: str = "text-embedding-3-small"
    dimension: int = 1536
    provider: str = "openai"


class VectorStoreConfig(BaseModel):
    """Configuration for vector stores."""
    type: str = "pgvector"
    path: Optional[str] = None
    dimension: int = 1536
    table_name: str = "documents"
    distance_metric: str = "cosine"


class RetrieverConfig(BaseModel):
    """Configuration for retrievers."""
    type: str = "hybrid"
    max_documents: int = 5
    min_score: float = 0.7
    vector_weight: float = 0.7
    keyword_weight: float = 0.3
    use_reranking: bool = False


class ContextConfig(BaseModel):
    """Configuration for context management."""
    max_tokens: int = 4000
    overlap: int = 200
    token_buffer: int = 1000


class RAGSettings(BaseSettings):
    """Unified RAG settings using Pydantic BaseSettings."""

    # Embedding settings
    embedding: EmbeddingConfig = Field(default_factory=EmbeddingConfig)

    # Vector store settings
    vector_store: VectorStoreConfig = Field(default_factory=VectorStoreConfig)

    # Retriever settings
    retriever: RetrieverConfig = Field(default_factory=RetrieverConfig)

    # Context settings
    context: ContextConfig = Field(default_factory=ContextConfig)

    # LLM settings
    llm: LLMConfig = Field(
        default_factory=lambda: LLMConfig(
            component_type="rag",
            temperature=0.3,
            max_tokens=1000,
            timeout=60.0,
            max_retries=3
        )
    )

    # Timeout settings (in seconds) - standardized values
    timeout_embedding: float = Field(default=30.0)
    timeout_vector_search: float = Field(default=10.0)
    timeout_keyword_search: float = Field(default=5.0)
    timeout_hybrid_search: float = Field(default=15.0)
    timeout_llm: float = Field(default=60.0)

    # Retry settings
    max_retries: int = Field(default=3)
    retry_delay: float = Field(default=1.0)
    retry_jitter: float = Field(default=0.1)

    class Config:
        env_prefix = "RAG_"
        case_sensitive = False


# Global unified settings instance
rag_settings = RAGSettings()


class RAGConfig:
    """Legacy configuration container for backward compatibility."""

    def __init__(self, **kwargs):
        """
        Initialize RAG configuration using unified settings.

        Args:
            **kwargs: Configuration options
        """
        # Embedding configuration
        self.embedding_provider = kwargs.get("embedding_provider", rag_settings.embedding.provider)
        self.embedding_model = kwargs.get("embedding_model", rag_settings.embedding.model_name)
        self.embedding_dimension = kwargs.get("embedding_dimension", rag_settings.embedding.dimension)

        # Vector store configuration
        self.vector_store_type = kwargs.get("vector_store_type", rag_settings.vector_store.type)
        self.vector_store_path = kwargs.get("vector_store_path", rag_settings.vector_store.path)
        self.vector_store_dimension = kwargs.get("vector_store_dimension", self.embedding_dimension)
        self.vector_store_table = kwargs.get("vector_store_table", rag_settings.vector_store.table_name)
        self.vector_store_distance = kwargs.get("vector_store_distance", rag_settings.vector_store.distance_metric)

        # Retriever configuration
        self.retriever_type = kwargs.get("retriever_type", rag_settings.retriever.type)
        self.vector_weight = kwargs.get("vector_weight", rag_settings.retriever.vector_weight)
        self.keyword_weight = kwargs.get("keyword_weight", rag_settings.retriever.keyword_weight)
        self.use_reranking = kwargs.get("use_reranking", rag_settings.retriever.use_reranking)

        # Context window configuration
        self.max_tokens = kwargs.get("max_tokens", rag_settings.context.max_tokens)
        self.token_buffer = kwargs.get("token_buffer", rag_settings.context.token_buffer)

        # Timeout configuration - using standardized values
        self.embedding_timeout = kwargs.get("embedding_timeout", rag_settings.timeout_embedding)
        self.vector_search_timeout = kwargs.get("vector_search_timeout", rag_settings.timeout_vector_search)
        self.keyword_search_timeout = kwargs.get("keyword_search_timeout", rag_settings.timeout_keyword_search)
        self.hybrid_search_timeout = kwargs.get("hybrid_search_timeout", rag_settings.timeout_hybrid_search)

        # Additional options
        self.additional_options = kwargs


async def initialize_embedding_model(config: Optional[RAGConfig] = None):
    """
    Initialize an embedding model.

    Args:
        config: RAG configuration

    Returns:
        Initialized embedding model
    """
    # Import here to avoid circular imports
    from ..embeddings import get_embedding_model

    if config is None:
        config = RAGConfig()

    logger.info(f"Initializing embedding model: {config.embedding_model}")
    return get_embedding_model(
        provider=config.embedding_provider,
        model=config.embedding_model,
        dimension=config.embedding_dimension
    )


async def initialize_knowledge_base_service(
    embedding_model = None,  # Type hint removed to avoid circular import
    vector_store = None,  # Type hint removed to avoid circular import
    config: Optional[RAGConfig] = None
):
    """
    Initialize a knowledge base service.

    Args:
        embedding_model: Optional embedding model
        vector_store: Optional vector store
        config: RAG configuration

    Returns:
        Initialized knowledge base service
    """
    if config is None:
        config = RAGConfig()

    # Import here to avoid circular imports
    from ..knowledge_base import KnowledgeBaseService
    from ..vector_store import get_vector_store

    # Initialize embedding model if not provided
    if embedding_model is None:
        embedding_model = await initialize_embedding_model(config)

    # Initialize vector store if not provided
    if vector_store is None:
        vector_store = await get_vector_store(
            store_type=config.vector_store_type,
            dimension=config.vector_store_dimension,
            table_name=config.vector_store_table,
            distance_metric=config.vector_store_distance
        )

    logger.info("Initializing knowledge base service")

    return KnowledgeBaseService(
        vector_store=vector_store,
        embedding_model=embedding_model,
        vector_weight=config.vector_weight,
        keyword_weight=config.keyword_weight,
        use_reranking=config.use_reranking
    ) 