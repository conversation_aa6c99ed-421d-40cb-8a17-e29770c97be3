"""
RAG (Retrieval-Augmented Generation) package.

This package provides components for document retrieval and generation:
- Vector stores for document storage and retrieval
- Embedding models for text vectorization
- Retrievers for document search
- Knowledge base for document management
"""

# Import only the consolidated config to avoid circular import issues
from .utils.config import rag_settings, RAGSettings

# Other imports available but not imported at package level to avoid import issues
# from .vector_store import VectorStore, PgVectorStore, get_vector_store
# from .embeddings import EmbeddingModel, OpenAIEmbedding, get_embedding_model
# from .retriever import Retriever, HybridRetriever, get_retriever
# from .knowledge_base import KnowledgeBaseService
# from .utils.config import initialize_knowledge_base_service

__all__ = [
    # Configuration - main consolidated exports
    "rag_settings",
    "RAGSettings",
]
